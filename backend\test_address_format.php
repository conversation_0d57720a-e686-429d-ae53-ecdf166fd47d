<?php
// Simple script to test the address format

// Get the raw input
$input = file_get_contents('php://input');
error_log('Raw input: ' . $input);

// Try to decode it
$data = json_decode($input, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    error_log('JSON decode error: ' . json_last_error_msg());
    echo json_encode([
        'status' => 'error',
        'message' => 'Invalid JSON: ' . json_last_error_msg()
    ]);
    exit;
}

// Check if address_data is present
if (empty($data['address_data']) || !is_array($data['address_data'])) {
    error_log('No address_data found or not an array');
    echo json_encode([
        'status' => 'error',
        'message' => 'No address_data found or not an array'
    ]);
    exit;
}

// Check required fields
$requiredFields = ['first_name', 'last_name', 'address_line_1', 'city', 'state', 'postal_code', 'country'];
$missingFields = [];

foreach ($requiredFields as $field) {
    if (empty($data['address_data'][$field])) {
        $missingFields[] = $field;
    }
}

if (!empty($missingFields)) {
    error_log('Missing required fields: ' . implode(', ', $missingFields));
    echo json_encode([
        'status' => 'error',
        'message' => 'Missing required fields: ' . implode(', ', $missingFields),
        'missing_fields' => $missingFields
    ]);
    exit;
}

// All good
error_log('Address data format is valid');
echo json_encode([
    'status' => 'success',
    'message' => 'Address data format is valid',
    'address_data' => $data['address_data']
]);