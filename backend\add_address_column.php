<?php
// Include the autoloader
require_once __DIR__ . '/vendor/autoload.php';

use Wolffoxx\Config\Database;

try {
    // Execute the SQL to add the address column
    $sql = "ALTER TABLE users ADD COLUMN address VARCHAR(500) DEFAULT NULL AFTER gender";
    $statement = Database::execute($sql);
    
    echo "Address column added successfully!\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    // If column already exists, this is fine
    if (strpos($e->getMessage(), 'Duplicate column') !== false) {
        echo "Column already exists, continuing...\n";
    }
}