<?php

echo "Testing Outfit Creation...\n\n";

// Test Outfit POST (Create outfit)
echo "Testing Outfit POST (Create outfit):\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/api/v1/outfits');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'name' => 'Test Outfit',
    'description' => 'A test outfit',
    'occasion' => 'casual',
    'season' => 'all',
    'is_public' => false,
    'items' => [
        [
            'product_id' => 1,
            'selected_color' => 'black',
            'selected_size' => 'M',
            'category_type' => 'top',
            'is_primary' => true
        ]
    ]
]));
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Status: $httpCode\n";
echo "Response: $response\n\n";

echo "Test Complete!\n";
