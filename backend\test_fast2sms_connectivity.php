<?php

/**
 * Fast2SMS Connectivity Test Script
 * 
 * This script tests the connectivity and DNS resolution for Fast2SMS API
 * to diagnose the "Could not resolve host: www.fast2sms.com" error.
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value, '"');
    }
}

echo "🔍 Fast2SMS Connectivity Diagnostic Test\n";
echo "=========================================\n\n";

// Test 1: DNS Resolution
echo "1. Testing DNS Resolution...\n";
$host = 'www.fast2sms.com';
$ip = gethostbyname($host);

if ($ip === $host) {
    echo "❌ DNS Resolution Failed: Cannot resolve $host\n";
    echo "   This explains the 'Could not resolve host' error.\n\n";
    
    // Try alternative hosts
    echo "   Trying alternative hosts...\n";
    $alternatives = ['fast2sms.com', 'api.fast2sms.com'];
    foreach ($alternatives as $alt) {
        $altIp = gethostbyname($alt);
        if ($altIp !== $alt) {
            echo "   ✅ $alt resolves to: $altIp\n";
        } else {
            echo "   ❌ $alt also fails to resolve\n";
        }
    }
} else {
    echo "✅ DNS Resolution Success: $host resolves to $ip\n";
}
echo "\n";

// Test 2: Basic connectivity test
echo "2. Testing Basic Connectivity...\n";
$testUrls = [
    'https://www.fast2sms.com',
    'https://fast2sms.com',
    'https://www.fast2sms.com/dev/bulkV2'
];

foreach ($testUrls as $url) {
    echo "Testing: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Wolffoxx-Test/1.0');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "   ❌ cURL Error: $error\n";
    } else {
        echo "   ✅ HTTP Response: $httpCode\n";
    }
}
echo "\n";

// Test 3: Test Fast2SMS API with actual request
echo "3. Testing Fast2SMS API Endpoint...\n";
$apiKey = $_ENV['FAST2SMS_API_KEY'] ?? '';

if (empty($apiKey)) {
    echo "❌ Fast2SMS API key not found in .env file\n\n";
} else {
    echo "API Key: " . substr($apiKey, 0, 10) . "...\n";
    
    // Test API endpoint with a simple request
    $endpoint = 'https://www.fast2sms.com/dev/bulkV2';
    $testData = [
        'message' => 'Test connectivity from Wolffoxx',
        'language' => 'english',
        'route' => 'q',
        'numbers' => '9999999999' // Test number
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'authorization: ' . $apiKey,
        'accept: */*',
        'cache-control: no-cache',
        'content-type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Wolffoxx-API/1.0');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    
    echo "API Test Results:\n";
    if ($error) {
        echo "   ❌ cURL Error: $error\n";
        echo "   This is likely the same error you're experiencing.\n";
    } else {
        echo "   ✅ Connection successful\n";
        echo "   HTTP Code: $httpCode\n";
        echo "   Response: " . substr($response, 0, 200) . "...\n";
    }
    
    echo "\nDetailed cURL Info:\n";
    echo "   Total Time: " . $info['total_time'] . "s\n";
    echo "   DNS Lookup Time: " . $info['namelookup_time'] . "s\n";
    echo "   Connect Time: " . $info['connect_time'] . "s\n";
}
echo "\n";

// Test 4: Network diagnostics
echo "4. Network Diagnostics...\n";
echo "Server IP: " . ($_SERVER['SERVER_ADDR'] ?? 'Unknown') . "\n";
echo "User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'CLI') . "\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "cURL Version: " . curl_version()['version'] . "\n";
echo "OpenSSL Version: " . (curl_version()['ssl_version'] ?? 'Not available') . "\n";
echo "\n";

// Test 5: Recommendations
echo "5. Troubleshooting Recommendations...\n";
echo "If you're getting 'Could not resolve host' errors:\n\n";

echo "A. DNS Issues:\n";
echo "   - Check if your server can resolve external domains\n";
echo "   - Try: ping www.fast2sms.com\n";
echo "   - Check /etc/resolv.conf for DNS servers\n\n";

echo "B. Firewall Issues:\n";
echo "   - Ensure outbound HTTPS (port 443) is allowed\n";
echo "   - Check if your hosting provider blocks external API calls\n\n";

echo "C. Alternative Solutions:\n";
echo "   - Try using 'fast2sms.com' instead of 'www.fast2sms.com'\n";
echo "   - Use a different SMS provider (MSG91 was working in your logs)\n";
echo "   - Contact your hosting provider about external API access\n\n";

echo "D. Quick Fix - Switch to MSG91:\n";
echo "   Your logs show MSG91 was working successfully.\n";
echo "   To switch back: SMS_PROVIDER=msg91 in .env\n\n";

echo "🔧 Diagnostic completed!\n";
