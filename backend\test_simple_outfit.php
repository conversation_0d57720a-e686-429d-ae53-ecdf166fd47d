<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/config/database.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Wolffoxx\Config\Database;

// Initialize database
Database::init();

echo "🔍 Testing simple outfit operations...\n";
echo "=====================================\n\n";

try {
    // Test 1: Check if outfits table exists and has data
    echo "1. Checking outfits table...\n";
    $stmt = Database::execute("SELECT COUNT(*) as count FROM outfits");
    $result = $stmt->fetch();
    echo "   Total outfits: {$result['count']}\n\n";
    
    // Test 2: Check if outfit_items table exists and has data
    echo "2. Checking outfit_items table...\n";
    $stmt = Database::execute("SELECT COUNT(*) as count FROM outfit_items");
    $result = $stmt->fetch();
    echo "   Total outfit items: {$result['count']}\n\n";
    
    // Test 3: Get recent outfit with items
    echo "3. Getting recent outfit with items...\n";
    $sql = "SELECT o.*, 
                   COUNT(oi.id) as item_count
            FROM outfits o 
            LEFT JOIN outfit_items oi ON o.id = oi.outfit_id 
            WHERE o.user_id = 3
            GROUP BY o.id 
            ORDER BY o.created_at DESC 
            LIMIT 1";
    
    $stmt = Database::execute($sql);
    $outfit = $stmt->fetch();
    
    if ($outfit) {
        echo "   Outfit found: {$outfit['name']} (ID: {$outfit['id']})\n";
        echo "   Items: {$outfit['item_count']}\n";
        echo "   Total Price: \${$outfit['total_price']}\n";
        echo "   Created: {$outfit['created_at']}\n\n";
        
        // Test 4: Get items for this outfit
        echo "4. Getting items for outfit {$outfit['id']}...\n";
        $itemSql = "SELECT oi.*, p.name as product_name, p.price as current_price
                   FROM outfit_items oi 
                   JOIN products p ON oi.product_id = p.id 
                   WHERE oi.outfit_id = ?";
        $itemStmt = Database::execute($itemSql, [$outfit['id']]);
        $items = $itemStmt->fetchAll();
        
        foreach ($items as $item) {
            echo "   - Product: {$item['product_name']} (ID: {$item['product_id']})\n";
            echo "     Selected Color: {$item['selected_color']}\n";
            echo "     Selected Color Hex: {$item['selected_color_hex']}\n";
            echo "     Selected Size: {$item['selected_size']}\n";
            echo "     Price at Addition: \${$item['price_at_addition']}\n";
            echo "     Current Price: \${$item['current_price']}\n\n";
        }
    } else {
        echo "   No outfits found for user 3\n\n";
    }
    
    // Test 5: Check product_images table
    echo "5. Checking product_images table...\n";
    try {
        $stmt = Database::execute("SELECT COUNT(*) as count FROM product_images");
        $result = $stmt->fetch();
        echo "   Total product images: {$result['count']}\n";
        
        if ($result['count'] > 0) {
            $stmt = Database::execute("SELECT * FROM product_images LIMIT 3");
            $images = $stmt->fetchAll();
            foreach ($images as $img) {
                echo "   - Product {$img['product_id']}: {$img['image_url']}\n";
            }
        }
    } catch (Exception $e) {
        echo "   Error: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
