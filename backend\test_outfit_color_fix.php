<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/config/database.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Wolffoxx\Config\Database;
use Wolffoxx\Models\Outfit;

// Initialize database
Database::init();

echo "🧪 Testing outfit color and price fixes...\n";
echo "==========================================\n\n";

try {
    $outfitModel = new Outfit();
    $userId = 3; // Test user
    
    // Create a new test outfit
    $outfitData = [
        'name' => 'Color & Price Test',
        'description' => 'Testing color hex and price calculation',
        'season' => 'all'
    ];
    
    echo "1. Creating new outfit...\n";
    $outfitId = $outfitModel->createOutfit($userId, $outfitData);
    echo "   Created outfit ID: $outfitId\n\n";
    
    // Add an item with color
    $itemData = [
        'product_id' => 1, // Oversized Tokyo Graphic Tee
        'selected_color' => 'Blue',
        'selected_size' => 'L'
    ];
    
    echo "2. Adding item with Blue color...\n";
    $result = $outfitModel->addItemToOutfit($outfitId, $itemData);
    echo "   Item added: " . ($result ? 'YES' : 'NO') . "\n\n";
    
    // Fetch the updated outfit
    echo "3. Fetching updated outfit data...\n";
    $outfits = $outfitModel->getUserOutfits($userId);
    $testOutfit = null;
    
    foreach ($outfits as $outfit) {
        if ($outfit['id'] == $outfitId) {
            $testOutfit = $outfit;
            break;
        }
    }
    
    if ($testOutfit) {
        echo "   Outfit Name: " . $testOutfit['name'] . "\n";
        echo "   Total Price: $" . $testOutfit['total_price'] . "\n";
        echo "   Total Sale Price: $" . $testOutfit['total_sale_price'] . "\n";
        echo "   Item Count: " . count($testOutfit['items']) . "\n\n";
        
        if (!empty($testOutfit['items'])) {
            $item = $testOutfit['items'][0];
            echo "   First Item Details:\n";
            echo "   - Product: " . $item['product_name'] . "\n";
            echo "   - Selected Color: " . $item['selected_color'] . "\n";
            echo "   - Selected Color Hex: " . ($item['selected_color_hex'] ?? 'NULL') . "\n";
            echo "   - Selected Size: " . $item['selected_size'] . "\n";
            echo "   - Price at Addition: $" . $item['price_at_addition'] . "\n";
            echo "   - Sale Price at Addition: $" . ($item['sale_price_at_addition'] ?? 'NULL') . "\n";
        }
    } else {
        echo "   ❌ Could not find test outfit\n";
    }
    
    // Clean up - delete the test outfit
    echo "\n4. Cleaning up test outfit...\n";
    $deleted = $outfitModel->deleteOutfit($outfitId);
    echo "   Deleted: " . ($deleted ? 'YES' : 'NO') . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
