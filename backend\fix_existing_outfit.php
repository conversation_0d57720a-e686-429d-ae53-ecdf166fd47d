<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/config/database.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Wolffoxx\Config\Database;
use Wolffoxx\Models\Outfit;

// Initialize database
Database::init();

echo "🔧 Fixing existing outfit data...\n";
echo "=================================\n\n";

try {
    $outfitModel = new Outfit();
    
    // Update totals for outfit ID 30 (the Color Test outfit)
    echo "1. Updating totals for outfit ID 30...\n";
    $result = $outfitModel->updateOutfitTotals(30);
    echo "   Update result: " . ($result ? 'SUCCESS' : 'FAILED') . "\n\n";
    
    // Update color hex for the existing item
    echo "2. Updating color hex for existing item...\n";
    $sql = "UPDATE outfit_items SET selected_color_hex = ? WHERE id = 5 AND selected_color = 'Red'";
    $stmt = Database::execute($sql, ['#DC2626']); // Red color hex
    echo "   Color hex updated: " . ($stmt->rowCount() > 0 ? 'SUCCESS' : 'NO CHANGES') . "\n\n";
    
    // Fetch updated data
    echo "3. Fetching updated outfit data...\n";
    $outfits = $outfitModel->getUserOutfits(3);
    
    foreach ($outfits as $outfit) {
        if ($outfit['id'] == 30) {
            echo "   Outfit: " . $outfit['name'] . "\n";
            echo "   Total Price: $" . $outfit['total_price'] . "\n";
            echo "   Total Sale Price: $" . $outfit['total_sale_price'] . "\n";
            
            if (!empty($outfit['items'])) {
                $item = $outfit['items'][0];
                echo "   Item Color: " . $item['selected_color'] . "\n";
                echo "   Item Color Hex: " . ($item['selected_color_hex'] ?? 'NULL') . "\n";
                echo "   Item Price: $" . $item['price_at_addition'] . "\n";
                echo "   Item Sale Price: $" . ($item['sale_price_at_addition'] ?? 'NULL') . "\n";
            }
            break;
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
