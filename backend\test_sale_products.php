<?php

echo "=== TESTING SALE PRODUCTS ===\n\n";

// Test 1: Check database directly
require_once 'config/database.php';
use Wolffoxx\Config\Database;

try {
    Database::init();
    
    echo "1. Checking database for sale products:\n";
    $stmt = Database::execute('SELECT COUNT(*) as count FROM products WHERE is_sale = 1');
    $result = $stmt->fetch();
    echo "   Sale products count: " . $result['count'] . "\n\n";
    
    echo "2. Sample products with sale info:\n";
    $stmt2 = Database::execute('SELECT id, name, price, sale_price, is_sale FROM products LIMIT 5');
    $products = $stmt2->fetchAll();
    foreach($products as $p) {
        echo "   - {$p['name']} (price: {$p['price']}, sale_price: {$p['sale_price']}, is_sale: {$p['is_sale']})\n";
    }
    
    echo "\n3. Let's set some products as sale items:\n";
    Database::execute('UPDATE products SET is_sale = 1, sale_price = price * 0.7 WHERE id IN (1, 2, 3)');
    echo "   Updated products 1, 2, 3 to be on sale\n";
    
    echo "\n4. Checking again:\n";
    $stmt3 = Database::execute('SELECT COUNT(*) as count FROM products WHERE is_sale = 1');
    $result3 = $stmt3->fetch();
    echo "   Sale products count now: " . $result3['count'] . "\n";
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}

echo "\n5. Testing API endpoint:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/api/v1/products?on_sale=true');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Status: $httpCode\n";
echo "Response: $response\n";

echo "\n=== TEST COMPLETE ===\n";
