<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/config/database.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Wolffoxx\Config\Database;

// Initialize database
Database::init();

echo "🔧 Fixing empty outfit names...\n";
echo "===============================\n\n";

try {
    // Fix outfits with empty string names
    echo "1. Fixing outfits with empty names...\n";
    $sql = "UPDATE outfits SET name = CONCAT('Outfit ', id) WHERE name = '' OR name IS NULL";
    $stmt = Database::execute($sql);
    echo "   Updated {$stmt->rowCount()} outfits\n\n";
    
    // Verify the fix
    echo "2. Verifying fixes...\n";
    $verifySql = "SELECT id, name, description, total_price, created_at FROM outfits ORDER BY created_at DESC";
    $verifyStmt = Database::execute($verifySql);
    $outfits = $verifyStmt->fetchAll();
    
    foreach ($outfits as $outfit) {
        echo "   ID: {$outfit['id']} - Name: '{$outfit['name']}' - Price: \${$outfit['total_price']}\n";
    }
    
    echo "\n✅ Fix completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
