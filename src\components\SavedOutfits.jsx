import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Shirt, Trash2, Eye, Star, Calendar, ShoppingBag } from 'lucide-react';
import { useCart } from '../context/CartContext';
import { useOutfit } from '../context/OutfitContext';
import { Link, useParams } from 'react-router-dom';

const SavedOutfits = ({ highlightOutfitId, currentProductId }) => {
  const [selectedOutfit, setSelectedOutfit] = useState(null);
  const [highlightedOutfit, setHighlightedOutfit] = useState(null);
  const [processingOutfits, setProcessingOutfits] = useState(new Set());
  const { addToCart } = useCart();
  const { savedOutfits, deleteOutfit } = useOutfit();
  const outfitRefs = useRef({});
  const { id: productId } = useParams(); // Get current product ID from URL

  // Check if current product is in any outfit
  const isProductInOutfit = (outfit) => {
    const currentId = currentProductId || productId;
    if (!currentId || !outfit.items) return false;

    return outfit.items.some(item =>
      (item.product_id && item.product_id.toString() === currentId.toString()) ||
      (item.id && item.id.toString() === currentId.toString())
    );
  };

  // Handle highlighting when navigated from product page
  useEffect(() => {
    if (highlightOutfitId) {
      setHighlightedOutfit(highlightOutfitId);

      // Scroll to the highlighted outfit
      setTimeout(() => {
        const outfitElement = outfitRefs.current[highlightOutfitId];
        if (outfitElement) {
          outfitElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 500);

      // Remove highlight after 3 seconds
      setTimeout(() => {
        setHighlightedOutfit(null);
      }, 3000);
    }
  }, [highlightOutfitId]);

  const addOutfitToCart = (outfit) => {
    const outfitKey = `${outfit.id || outfit.name}`;

    // Prevent rapid double-clicks
    if (processingOutfits.has(outfitKey)) {
      console.log('⚠️ OUTFIT ALREADY PROCESSING - Skipping:', outfitKey);
      return;
    }

    // Mark outfit as processing
    setProcessingOutfits(prev => new Set([...prev, outfitKey]));

    console.log('🛒 SAVED OUTFIT TO CART - Starting process:', {
      outfitName: outfit.name,
      itemCount: outfit.items.length,
      items: outfit.items.map(item => ({ id: item.product_id || item.id, name: item.product_name || item.name }))
    });

    // Process all items at once without delays
    outfit.items.forEach((item, index) => {
      // Use the selected color or matching color
      const selectedColor = item.selectedColor || item.matchingColor || item.colors?.[0];

      // Format the product correctly for the cart
      const rawPrice = item.sale_price_at_addition || item.current_sale_price || item.salePrice ||
                      item.price_at_addition || item.current_price || item.price || 0;
      const numericPrice = parseFloat(rawPrice) || 0;

      const cartProduct = {
        id: item.product_id || item.id,
        name: item.name || item.product_name,
        price: numericPrice,
        color: (typeof selectedColor === 'object' ? selectedColor?.name : selectedColor || item.selected_color) || 'Default',
        size: item.selectedSize || item.selected_size || item.sizes?.[0] || 'M',
        image: (typeof selectedColor === 'object' ? selectedColor?.images?.[0] : null) || item.images?.[0] || item.product_image || item.image,
        outfitName: outfit.name, // Group items under outfit name
        category: item.category
      };

      console.log(`🛒 SAVED OUTFIT TO CART - Adding item ${index + 1}/${outfit.items.length}:`, cartProduct);
      addToCart(cartProduct, 1); // Add 1 quantity of each item
    });

    // Remove from processing after a short delay
    setTimeout(() => {
      setProcessingOutfits(prev => {
        const newSet = new Set(prev);
        newSet.delete(outfitKey);
        return newSet;
      });
    }, 1000); // 1 second delay
  };

  if (savedOutfits.length === 0) {
    return (
      <div className="text-center py-12">
        <Shirt size={48} className="mx-auto text-[#AAAAAA] mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">No Saved Outfits</h3>
        <p className="text-[#AAAAAA]">Start building outfits to see them here!</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white mb-6">Your Saved Outfits</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {savedOutfits.map((outfit) => {
          const hasCurrentProduct = isProductInOutfit(outfit);
          const isHighlighted = highlightedOutfit === outfit.id;

          return (
            <motion.div
              key={outfit.id}
              ref={(el) => (outfitRefs.current[outfit.id] = el)}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{
                opacity: 1,
                scale: 1,
                boxShadow: isHighlighted
                  ? '0 0 20px rgba(33, 79, 195, 0.4)'
                  : hasCurrentProduct
                  ? '0 0 15px rgba(34, 197, 94, 0.3)'
                  : '0 0 0px rgba(33, 79, 195, 0)'
              }}
              className={`bg-[#1a1a1a] rounded-xl p-4 border transition-all duration-500 ${
                isHighlighted
                  ? 'border-blue-500/80 bg-[#2a2a2a]'
                  : hasCurrentProduct
                  ? 'border-green-500/80 bg-[#1a2a1a]'
                  : 'border-[#404040] hover:border-blue-500/50'
              }`}
            >
              {/* Outfit Header */}
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-white font-semibold">{outfit.name}</h3>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setSelectedOutfit(outfit)}
                    className="p-2 text-[#AAAAAA] hover:text-white transition-colors"
                  >
                    <Eye size={16} />
                  </button>
                  <button
                    onClick={() => deleteOutfit(outfit.id)}
                    className="p-2 text-[#AAAAAA] hover:text-red-400 transition-colors"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>

              {/* Outfit Preview - Consistent Layout */}
              <div className="mb-4">
                {/* Always use 3-column grid for consistency */}
                <div className="grid grid-cols-3 gap-2 mb-2 h-24">
                  {/* Show first 3 items or placeholders */}
                  {Array.from({ length: 3 }).map((_, index) => {
                    const item = (outfit.items || [])[index];

                    if (!item) {
                      // Empty placeholder for consistent layout
                      return (
                        <div key={index} className="bg-[#2a2a2a] rounded-lg border border-[#404040] opacity-30"></div>
                      );
                    }

                    return (
                      <div key={index} className="relative">
                        <img
                          src={
                            item.selectedColor?.images?.[0] ||
                            item.matchingColor?.images?.[0] ||
                            item.images?.[0] ||
                            item.product_image ||
                            (item.product_id ? `https://images.unsplash.com/photo-1583743814966-8936f5b7be1a?q=80&w=700&auto=format&fit=crop&ixlib=rb-4.0.3` : '/placeholder-image.jpg')
                          }
                          alt={item.name || item.product_name}
                          className="w-full aspect-square object-cover rounded-lg"
                        />
                        {/* Color indicator overlay */}
                        {(item.selectedColor || item.selected_color || item.matchingColor || item.colors?.[0]) && (
                          <div className="absolute bottom-1 right-1">
                            <div
                              className="w-3 h-3 rounded-full border border-white/50 shadow-sm"
                              style={{
                                backgroundColor:
                                  item.selected_color_hex ||
                                  (typeof item.selectedColor === 'object' ? item.selectedColor?.hex || item.selectedColor?.value : null) ||
                                  (typeof item.matchingColor === 'object' ? item.matchingColor?.hex || item.matchingColor?.value : null) ||
                                  item.colors?.[0]?.hex || item.colors?.[0]?.value ||
                                  (item.selected_color === 'Black' ? '#000000' :
                                   item.selected_color === 'White' ? '#FFFFFF' :
                                   item.selected_color === 'Blue' ? '#2563EB' :
                                   item.selected_color === 'Red' ? '#DC2626' :
                                   '#666666') // fallback color with basic color mapping
                              }}
                              title={`Color: ${
                                (typeof item.selectedColor === 'object' ? item.selectedColor?.name : item.selectedColor || item.selected_color) ||
                                (typeof item.matchingColor === 'object' ? item.matchingColor?.name : item.matchingColor) ||
                                item.colors?.[0]?.name ||
                                'Unknown'
                              }`}
                            />
                          </div>
                        )}

                        {/* Show +N indicator on third item if more items exist */}
                        {index === 2 && (outfit.items || []).length > 3 && (
                          <div className="absolute inset-0 bg-black/60 rounded-lg flex items-center justify-center">
                            <span className="text-white text-sm font-medium">
                              +{(outfit.items || []).length - 3}
                            </span>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Outfit Info */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-[#AAAAAA]">Items:</span>
                  <span className="text-white">{(outfit.items || []).length}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-[#AAAAAA]">Total:</span>
                  <span className="text-white font-semibold">${
                    (() => {
                      // Calculate total from items if no stored total
                      if (outfit.total_sale_price) return parseFloat(outfit.total_sale_price).toFixed(2);
                      if (outfit.total_price) return parseFloat(outfit.total_price).toFixed(2);
                      if (outfit.totalPrice) return parseFloat(outfit.totalPrice).toFixed(2);

                      // Calculate from items
                      if (outfit.items && outfit.items.length > 0) {
                        const total = outfit.items.reduce((sum, item) => {
                          const price = parseFloat(
                            item.sale_price_at_addition ||
                            item.current_sale_price ||
                            item.salePrice ||
                            item.price_at_addition ||
                            item.current_price ||
                            item.price ||
                            0
                          );
                          return sum + price;
                        }, 0);
                        return total.toFixed(2);
                      }

                      return '0.00';
                    })()
                  }</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-[#AAAAAA]">Created:</span>
                  <span className="text-white">{new Date(outfit.createdAt || outfit.created_at).toLocaleDateString()}</span>
                </div>
              </div>

              {/* Add to Cart Button */}
              <button
                onClick={() => addOutfitToCart(outfit)}
                disabled={processingOutfits.has(`${outfit.id || outfit.name}`)}
                className={`w-full text-white py-2 px-4 rounded-lg font-medium transition-all duration-300 flex items-center justify-center gap-2 ${
                  processingOutfits.has(`${outfit.id || outfit.name}`)
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:shadow-lg hover:shadow-blue-500/25'
                }`}
                style={{
                  background: 'linear-gradient(to bottom right, #FF6B35 0%, #F7931E 100%)'
                }}
              >
                <ShoppingBag size={16} />
                {processingOutfits.has(`${outfit.id || outfit.name}`) ? 'Adding...' : 'Add to Cart'}
              </button>
            </motion.div>
          );
        })}
      </div>

      {/* Outfit Detail Modal */}
      <AnimatePresence>
        {selectedOutfit && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
              onClick={() => setSelectedOutfit(null)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="fixed inset-2 sm:inset-4 md:inset-6 lg:inset-8 bg-[#1a1a1a] backdrop-blur-xl border border-[#404040] rounded-xl z-50 overflow-hidden flex flex-col max-h-[95vh]"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-[#2a2a2a] flex-shrink-0">
                <h2 className="text-lg font-bold text-white">{selectedOutfit.name}</h2>
                <button
                  onClick={() => setSelectedOutfit(null)}
                  className="p-1.5 text-[#AAAAAA] hover:text-white transition-colors"
                >
                  ×
                </button>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto p-4">
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {(selectedOutfit.items || []).map((item, index) => {
                    // Check if this is the current product being viewed
                    const isCurrentProduct = window.location.pathname.includes(`/product/${item.product_id || item.id}`);

                    return (
                      <Link
                        key={index}
                        to={`/product/${item.product_id || item.id}`}
                        className={`rounded-xl p-3 transition-colors duration-300 block ${
                          isCurrentProduct
                            ? 'bg-green-600/20 border border-green-500/30 hover:bg-green-600/30'
                            : 'bg-[#2a2a2a] hover:bg-[#404040]'
                        }`}
                      >
                      <img
                        src={
                          item.images?.[0] ||
                          item.product_image ||
                          (item.product_id ? `https://images.unsplash.com/photo-1583743814966-8936f5b7be1a?q=80&w=700&auto=format&fit=crop&ixlib=rb-4.0.3` : '/placeholder-image.jpg')
                        }
                        alt={item.name || item.product_name}
                        className="w-full aspect-square object-cover rounded-lg mb-2"
                      />
                      <h4 className="text-white text-sm font-medium line-clamp-2 mb-1 hover:text-cyan-400 transition-colors">
                        {item.name || item.product_name}
                      </h4>
                      <p className="text-[#AAAAAA] text-xs mb-1">{item.category}</p>

                      {/* Color Indicator */}
                      {(item.selectedColor || item.selected_color || item.matchingColor || item.colors?.[0]) && (
                        <div className="flex items-center gap-2 mb-1">
                          <div
                            className="w-3 h-3 rounded-full border border-white/30"
                            style={{
                              backgroundColor:
                                item.selected_color_hex ||
                                (typeof item.selectedColor === 'object' ? item.selectedColor?.hex || item.selectedColor?.value : null) ||
                                (typeof item.matchingColor === 'object' ? item.matchingColor?.hex || item.matchingColor?.value : null) ||
                                item.colors?.[0]?.hex || item.colors?.[0]?.value ||
                                (item.selected_color === 'Black' ? '#000000' :
                                 item.selected_color === 'White' ? '#FFFFFF' :
                                 item.selected_color === 'Blue' ? '#2563EB' :
                                 item.selected_color === 'Red' ? '#DC2626' :
                                 '#666666') // fallback color with basic color mapping
                            }}
                            title={`Color: ${
                              (typeof item.selectedColor === 'object' ? item.selectedColor?.name : item.selectedColor || item.selected_color) ||
                              (typeof item.matchingColor === 'object' ? item.matchingColor?.name : item.matchingColor) ||
                              item.colors?.[0]?.name ||
                              'Unknown'
                            }`}
                          />
                          <span className="text-[#AAAAAA] text-xs">
                            {(typeof item.selectedColor === 'object' ? item.selectedColor?.name : item.selectedColor || item.selected_color) ||
                             (typeof item.matchingColor === 'object' ? item.matchingColor?.name : item.matchingColor) ||
                             item.colors?.[0]?.name ||
                             'Unknown'}
                          </span>
                        </div>
                      )}

                      <p className="text-orange-400 font-semibold text-sm">${
                        item.sale_price_at_addition ||
                        item.current_sale_price ||
                        item.price_at_addition ||
                        item.current_price ||
                        item.price ||
                        '0.00'
                      }</p>
                    </Link>
                  );
                  })}
                </div>
              </div>

              {/* Footer */}
              <div className="border-t border-[#2a2a2a] p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-[#AAAAAA] text-sm">Total: <span className="text-white font-semibold">${
                      (() => {
                        // Calculate total from items if no stored total
                        if (selectedOutfit.total_sale_price) return parseFloat(selectedOutfit.total_sale_price).toFixed(2);
                        if (selectedOutfit.total_price) return parseFloat(selectedOutfit.total_price).toFixed(2);
                        if (selectedOutfit.totalPrice) return parseFloat(selectedOutfit.totalPrice).toFixed(2);

                        // Calculate from items
                        if (selectedOutfit.items && selectedOutfit.items.length > 0) {
                          const total = selectedOutfit.items.reduce((sum, item) => {
                            const price = parseFloat(
                              item.sale_price_at_addition ||
                              item.current_sale_price ||
                              item.salePrice ||
                              item.price_at_addition ||
                              item.current_price ||
                              item.price ||
                              0
                            );
                            return sum + price;
                          }, 0);
                          return total.toFixed(2);
                        }

                        return '0.00';
                      })()
                    }</span></p>
                    <p className="text-[#AAAAAA] text-sm">{(selectedOutfit.items || []).length} items</p>
                  </div>
                  <button
                    onClick={() => {
                      addOutfitToCart(selectedOutfit);
                      setSelectedOutfit(null);
                    }}
                    disabled={processingOutfits.has(`${selectedOutfit?.id || selectedOutfit?.name}`)}
                    className={`text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 flex items-center gap-2 ${
                      processingOutfits.has(`${selectedOutfit?.id || selectedOutfit?.name}`)
                        ? 'opacity-50 cursor-not-allowed'
                        : 'hover:shadow-lg hover:shadow-blue-500/25'
                    }`}
                    style={{
                      background: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)'
                    }}
                  >
                    <ShoppingBag size={18} />
                    {processingOutfits.has(`${selectedOutfit?.id || selectedOutfit?.name}`) ? 'Adding...' : 'Add All to Cart'}
                  </button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SavedOutfits;
