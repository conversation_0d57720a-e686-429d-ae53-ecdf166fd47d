
const cloudinary = require('cloudinary').v2;
const fs = require('fs');
const path = require('path');

cloudinary.config({
  cloud_name: 'dsp0zmfcx',
  api_key: '336461674786964',
  api_secret: 'qAUvO-stA_YrE6f3e1hBmw4hpYc'
});

const folderPath = './ProductsImages';
const cloudFolder = 'ProductsImages';

fs.readdir(folderPath, (err, files) => {
  if (err) return console.error('Failed to list directory:', err);

  files.forEach((file) => {
    const filePath = path.join(folderPath, file);
    if (fs.lstatSync(filePath).isFile()) {
      cloudinary.uploader.upload(filePath, {
        folder: cloudFolder,
        use_filename: true,
        unique_filename: false
      }).then(result => {
        console.log(`✅ Uploaded: ${file} -> ${result.secure_url}`);
      }).catch(err => {
        console.error(`❌ Failed to upload ${file}:`, err);
      });
    }
  });
});
