<?php

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Create PDO connection directly
$host = $_ENV['DB_HOST'] ?? 'localhost';
$dbname = $_ENV['DB_NAME'] ?? 'wolffoxx';
$username = $_ENV['DB_USER'] ?? 'root';
$password = $_ENV['DB_PASS'] ?? '';

$dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
$options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
];

try {
    $pdo = new PDO($dsn, $username, $password, $options);
    
    // First, get a user ID to use
    $stmt = $pdo->query("SELECT id FROM users LIMIT 1");
    $user = $stmt->fetch();
    
    if (!$user) {
        die("No users found in the database\n");
    }
    
    $userId = $user['id'];
    echo "Using user ID: $userId\n";
    
    // Insert a test address
    $sql = "INSERT INTO user_addresses (
        user_id, type, is_default, 
        first_name, last_name, 
        address_line_1, city, state, postal_code, country
    ) VALUES (
        :user_id, 'shipping', 1,
        'Test', 'User',
        '123 Test Street', 'Test City', 'Test State', '12345', 'US'
    )";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':user_id' => $userId
    ]);
    
    $addressId = $pdo->lastInsertId();
    
    if ($addressId) {
        echo "Successfully added address with ID: $addressId\n";
        
        // Verify the address was added
        $stmt = $pdo->prepare("SELECT * FROM user_addresses WHERE id = ?");
        $stmt->execute([$addressId]);
        $address = $stmt->fetch();
        
        if ($address) {
            echo "Address details:\n";
            print_r($address);
        } else {
            echo "Could not retrieve the address that was just added\n";
        }
    } else {
        echo "Failed to add address - no ID returned\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}