<?php

// Define paths
define('ROOT_PATH', __DIR__);
define('SRC_PATH', ROOT_PATH . '/src');

// Load Composer autoloader first
require_once ROOT_PATH . '/vendor/autoload.php';

// Autoloader for our custom classes
spl_autoload_register(function ($class) {
    // Convert namespace to file path
    $prefix = 'Wolffoxx\\';
    $baseDir = SRC_PATH . '/';

    // Check if class uses our namespace
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }

    // Get relative class name
    $relativeClass = substr($class, $len);

    // Replace namespace separators with directory separators
    $file = $baseDir . str_replace('\\', '/', $relativeClass) . '.php';

    // Load the file if it exists
    if (file_exists($file)) {
        require $file;
    }
});

use Wolffoxx\Config\Database;
use Wolffoxx\Models\Review;

try {
    // Load environment variables
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    $dotenv->load();
    
    // Initialize database
    Database::init();
    
    echo "Testing Review model...\n";
    
    // Create review model
    $reviewModel = new Review();
    
    echo "Review model created successfully\n";
    
    // Test getting reviews for product 1
    echo "Getting reviews for product 1...\n";
    $reviews = $reviewModel->getProductReviews(1);
    echo "Found " . count($reviews) . " reviews\n";
    
    // Test creating a review
    echo "Creating a test review...\n";
    $reviewData = [
        'product_id' => 4,
        'user_id' => 3,
        'rating' => 5,
        'title' => 'Test Review',
        'comment' => 'This is a test review created via PHP script',
        'is_verified_purchase' => false
    ];
    
    $newReview = $reviewModel->create($reviewData);
    
    if ($newReview) {
        echo "Review created successfully with ID: " . $newReview['id'] . "\n";
    } else {
        echo "Failed to create review\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
