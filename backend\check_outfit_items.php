<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/config/database.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Wolffoxx\Config\Database;

// Initialize database
Database::init();

echo "🔍 Checking outfit items for recent outfits...\n";
echo "===============================================\n\n";

try {
    // Get recent outfits with their items
    $sql = "SELECT o.id, o.name, o.created_at, 
                   COUNT(oi.id) as item_count
            FROM outfits o 
            LEFT JOIN outfit_items oi ON o.id = oi.outfit_id 
            GROUP BY o.id 
            ORDER BY o.created_at DESC 
            LIMIT 5";
    
    $stmt = Database::execute($sql);
    $outfits = $stmt->fetchAll();
    
    foreach ($outfits as $outfit) {
        echo "Outfit ID: {$outfit['id']}\n";
        echo "Name: {$outfit['name']}\n";
        echo "Created: {$outfit['created_at']}\n";
        echo "Items: {$outfit['item_count']}\n";
        
        if ($outfit['item_count'] > 0) {
            // Get item details
            $itemSql = "SELECT oi.*, p.name as product_name 
                       FROM outfit_items oi 
                       JOIN products p ON oi.product_id = p.id 
                       WHERE oi.outfit_id = ?";
            $itemStmt = Database::execute($itemSql, [$outfit['id']]);
            $items = $itemStmt->fetchAll();
            
            foreach ($items as $item) {
                echo "  - Product: {$item['product_name']} (ID: {$item['product_id']})\n";
                echo "    Color: {$item['selected_color']}, Size: {$item['selected_size']}\n";
                echo "    Price: \${$item['price_at_addition']}\n";
            }
        }
        echo "---\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
