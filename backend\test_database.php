<?php

require_once __DIR__ . '/vendor/autoload.php';

use Wolffoxx\Config\Database;

try {
    // Load environment variables
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    $dotenv->load();
    
    // Initialize database
    Database::init();
    
    echo "Testing database connection...\n";
    
    // Test connection
    $pdo = Database::getConnection();
    echo "Database connected successfully\n";
    
    // Test query
    $stmt = Database::execute("SELECT COUNT(*) as count FROM reviews");
    $result = $stmt->fetch();
    echo "Reviews count: " . $result['count'] . "\n";
    
    // Test insert
    $sql = "INSERT INTO reviews (uuid, product_id, user_id, rating, title, comment, is_approved) VALUES (?, ?, ?, ?, ?, ?, ?)";
    $params = [
        'test-' . uniqid(),
        4,
        3,
        5,
        'Test Review',
        'This is a test review',
        1
    ];
    
    echo "Testing insert...\n";
    $stmt = Database::execute($sql, $params);
    echo "Insert successful, rows affected: " . $stmt->rowCount() . "\n";
    
    $lastId = Database::lastInsertId();
    echo "Last insert ID: " . $lastId . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
