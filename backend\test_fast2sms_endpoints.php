<?php

/**
 * Fast2SMS Endpoint Testing
 * 
 * This script tests different Fast2SMS API endpoints to find the correct one
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value, '"');
    }
}

echo "🔍 Fast2SMS Endpoint Testing\n";
echo "============================\n\n";

$apiKey = $_ENV['FAST2SMS_API_KEY'] ?? '';

if (empty($apiKey)) {
    echo "❌ Fast2SMS API key not found\n";
    exit(1);
}

echo "API Key: " . substr($apiKey, 0, 10) . "...\n\n";

// Test different endpoints
$endpoints = [
    'Current (301 error)' => 'https://fast2sms.com/dev/bulkV2',
    'Alternative 1' => 'https://www.fast2sms.com/dev/bulkV2',
    'Alternative 2' => 'https://api.fast2sms.com/bulk',
    'Alternative 3' => 'https://www.fast2sms.com/dev/bulk',
    'Alternative 4' => 'https://fast2sms.com/dev/bulk',
    'Alternative 5' => 'https://www.fast2sms.com/dev/v3/bulk',
    'Alternative 6' => 'https://fast2sms.com/dev/v3/bulk'
];

$testData = [
    'message' => 'Test OTP: 123456',
    'language' => 'english',
    'route' => 'q',
    'numbers' => '9999999999'
];

foreach ($endpoints as $name => $endpoint) {
    echo "Testing: $name\n";
    echo "URL: $endpoint\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false); // Don't follow redirects
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'authorization: ' . $apiKey,
        'accept: */*',
        'cache-control: no-cache',
        'content-type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Wolffoxx-API/1.0');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $redirectUrl = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
    curl_close($ch);
    
    if ($error) {
        echo "   ❌ cURL Error: $error\n";
    } else {
        echo "   HTTP Code: $httpCode\n";
        
        if ($httpCode == 301 || $httpCode == 302) {
            echo "   🔄 Redirect to: $redirectUrl\n";
        } elseif ($httpCode == 200) {
            $result = json_decode($response, true);
            if (isset($result['return'])) {
                echo "   ✅ Valid API response: " . ($result['return'] ? 'Success' : 'Error') . "\n";
                if (isset($result['message'])) {
                    echo "   Message: " . $result['message'] . "\n";
                }
            } else {
                echo "   Response: " . substr($response, 0, 100) . "...\n";
            }
        } else {
            echo "   Response: " . substr($response, 0, 100) . "...\n";
        }
    }
    echo "\n";
}

echo "🔍 Testing with different data formats...\n\n";

// Test with OTP-specific format
$otpEndpoints = [
    'OTP Format 1' => 'https://www.fast2sms.com/dev/bulkV2',
    'OTP Format 2' => 'https://fast2sms.com/dev/bulkV2'
];

$otpData = [
    'variables_values' => '123456',
    'route' => 'otp',
    'numbers' => '9999999999'
];

foreach ($otpEndpoints as $name => $endpoint) {
    echo "Testing: $name (OTP format)\n";
    echo "URL: $endpoint\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($otpData));
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // Follow redirects this time
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'authorization: ' . $apiKey,
        'accept: */*',
        'cache-control: no-cache',
        'content-type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Wolffoxx-API/1.0');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "   ❌ cURL Error: $error\n";
    } else {
        echo "   HTTP Code: $httpCode\n";
        
        if ($httpCode == 200) {
            $result = json_decode($response, true);
            if (isset($result['return'])) {
                echo "   ✅ Valid API response: " . ($result['return'] ? 'Success' : 'Error') . "\n";
                if (isset($result['message'])) {
                    echo "   Message: " . $result['message'] . "\n";
                }
            } else {
                echo "   Response: " . substr($response, 0, 200) . "...\n";
            }
        } else {
            echo "   Response: " . substr($response, 0, 100) . "...\n";
        }
    }
    echo "\n";
}

echo "🎯 Recommendation: Use the endpoint that returns HTTP 200 with valid JSON response\n";
