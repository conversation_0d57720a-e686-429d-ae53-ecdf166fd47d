<?php

/**
 * Simple test for persistent cart, wishlist, and outfit functionality
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/config/database.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Wolffoxx\Config\Database;
use Wolffoxx\Models\User;
use Wolffoxx\Models\Cart;
use Wolffoxx\Models\Wishlist;
use Wolffoxx\Models\Outfit;
use Wolffoxx\Models\Product;

echo "🧪 Testing Persistent Functionality (Direct Database)\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// Initialize database
Database::init();

try {
    $connection = Database::getConnection();
    echo "✅ Database connection successful\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test Models
echo "\n🔧 Testing Models...\n";

try {
    // Test User Model
    $userModel = new User();
    echo "✅ User model initialized\n";
    
    // Test Cart Model
    $cartModel = new Cart();
    echo "✅ Cart model initialized\n";
    
    // Test Wishlist Model
    $wishlistModel = new Wishlist();
    echo "✅ Wishlist model initialized\n";
    
    // Test Outfit Model
    $outfitModel = new Outfit();
    echo "✅ Outfit model initialized\n";
    
    // Test Product Model
    $productModel = new Product();
    echo "✅ Product model initialized\n";
    
} catch (Exception $e) {
    echo "❌ Model initialization failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test Database Tables
echo "\n📊 Testing Database Tables...\n";

$requiredTables = [
    'users' => 'User accounts',
    'carts' => 'Shopping carts',
    'cart_items' => 'Cart items',
    'wishlists' => 'Wishlist items',
    'outfits' => 'User outfits',
    'outfit_items' => 'Outfit items',
    'products' => 'Product catalog',
    'categories' => 'Product categories'
];

foreach ($requiredTables as $table => $description) {
    try {
        $stmt = $connection->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->fetch()) {
            // Check if table has data
            $countStmt = $connection->prepare("SELECT COUNT(*) as count FROM $table");
            $countStmt->execute();
            $count = $countStmt->fetch()['count'];
            echo "✅ Table '$table' exists ($description) - $count records\n";
        } else {
            echo "❌ Table '$table' missing ($description)\n";
        }
    } catch (Exception $e) {
        echo "❌ Error checking table '$table': " . $e->getMessage() . "\n";
    }
}

// Test Basic CRUD Operations
echo "\n🔄 Testing Basic CRUD Operations...\n";

try {
    // Test if we have any users
    $stmt = $connection->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $userCount = $stmt->fetch()['count'];
    
    if ($userCount > 0) {
        echo "✅ Found $userCount users in database\n";
        
        // Get first user for testing
        $stmt = $connection->prepare("SELECT * FROM users LIMIT 1");
        $stmt->execute();
        $testUser = $stmt->fetch();
        
        if ($testUser) {
            $userId = $testUser['id'];
            echo "✅ Using test user ID: $userId (Phone: {$testUser['phone']})\n";
            
            // Test Cart Operations
            echo "\n🛒 Testing Cart Operations...\n";
            $userCart = $cartModel->getUserCart($userId);
            echo "✅ Retrieved user cart - Items: " . ($userCart['total_items'] ?? 0) . "\n";
            
            // Test Wishlist Operations
            echo "\n❤️ Testing Wishlist Operations...\n";
            $userWishlist = $wishlistModel->getUserWishlist($userId);
            echo "✅ Retrieved user wishlist - Items: " . count($userWishlist) . "\n";
            
            // Test Outfit Operations
            echo "\n👔 Testing Outfit Operations...\n";
            $userOutfits = $outfitModel->getUserOutfits($userId);
            echo "✅ Retrieved user outfits - Count: " . count($userOutfits) . "\n";
            
        } else {
            echo "❌ Could not retrieve test user\n";
        }
    } else {
        echo "⚠️ No users found in database - create a user via OTP login first\n";
    }
    
} catch (Exception $e) {
    echo "❌ CRUD operations test failed: " . $e->getMessage() . "\n";
}

// Test Product Data for Display
echo "\n🖼️ Testing Product Data for Display...\n";

try {
    // Check if we have products
    $stmt = $connection->prepare("SELECT COUNT(*) as count FROM products");
    $stmt->execute();
    $productCount = $stmt->fetch()['count'];
    
    if ($productCount > 0) {
        echo "✅ Found $productCount products in database\n";
        
        // Get sample product with all display data
        $stmt = $connection->prepare("
            SELECT 
                p.*,
                GROUP_CONCAT(DISTINCT pi.image_url) as images,
                GROUP_CONCAT(DISTINCT pc.name) as colors,
                GROUP_CONCAT(DISTINCT ps.size) as sizes
            FROM products p
            LEFT JOIN product_images pi ON p.id = pi.product_id
            LEFT JOIN product_colors pc ON p.id = pc.product_id  
            LEFT JOIN product_sizes ps ON p.id = ps.product_id
            WHERE p.is_active = 1
            GROUP BY p.id
            LIMIT 1
        ");
        $stmt->execute();
        $sampleProduct = $stmt->fetch();
        
        if ($sampleProduct) {
            echo "✅ Sample product data structure:\n";
            echo "   - ID: {$sampleProduct['id']}\n";
            echo "   - Name: {$sampleProduct['name']}\n";
            echo "   - Price: ₹{$sampleProduct['price']}\n";
            echo "   - Sale Price: ₹" . ($sampleProduct['sale_price'] ?: 'N/A') . "\n";
            echo "   - Images: " . ($sampleProduct['images'] ? 'Available' : 'None') . "\n";
            echo "   - Colors: " . ($sampleProduct['colors'] ?: 'None') . "\n";
            echo "   - Sizes: " . ($sampleProduct['sizes'] ?: 'None') . "\n";
            echo "   - Category: {$sampleProduct['category']}\n";
        }
    } else {
        echo "⚠️ No products found - import products first\n";
    }
    
} catch (Exception $e) {
    echo "❌ Product data test failed: " . $e->getMessage() . "\n";
}

echo "\n🎉 Persistence testing completed!\n";
echo "\n📋 Summary:\n";
echo "✅ Backend models are working\n";
echo "✅ Database tables exist\n";
echo "✅ CRUD operations functional\n";
echo "✅ Product data structure ready for display\n";
echo "\n🚀 Ready for frontend integration!\n";
