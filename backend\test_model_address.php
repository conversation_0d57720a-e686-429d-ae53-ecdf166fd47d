<?php

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Import the UserAddress model
use Wolffoxx\Models\UserAddress;
use Wolffoxx\Models\User;

try {
    // Create model instances
    $userModel = new User();
    $addressModel = new UserAddress();
    
    // Get a user ID to use
    $users = $userModel->findAll([], [], 1);
    
    if (empty($users)) {
        die("No users found in the database\n");
    }
    
    $userId = $users[0]['id'];
    echo "Using user ID: $userId\n";
    
    // Create address data
    $addressData = [
        'user_id' => $userId,
        'type' => 'shipping',
        'is_default' => true,
        'first_name' => 'Test',
        'last_name' => 'User',
        'address_line_1' => '123 Test Street',
        'city' => 'Test City',
        'state' => 'Test State',
        'postal_code' => '12345',
        'country' => 'US'
    ];
    
    // Add the address
    echo "Adding address...\n";
    $address = $addressModel->addAddress($addressData);
    
    if ($address) {
        echo "Successfully added address with ID: " . $address['id'] . "\n";
        echo "Address details:\n";
        print_r($address);
    } else {
        echo "Failed to add address\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}