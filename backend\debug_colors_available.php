<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/config/database.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Wolffoxx\Config\Database;

// Initialize database
Database::init();

echo "🎨 Debugging available colors...\n";
echo "================================\n\n";

try {
    // Check colors in outfit_items
    $stmt = Database::execute('SELECT DISTINCT selected_color FROM outfit_items WHERE selected_color IS NOT NULL');
    $outfitColors = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Colors in outfit_items: " . implode(', ', $outfitColors) . "\n\n";
    
    // Check colors in products
    $stmt2 = Database::execute('SELECT DISTINCT colors FROM products WHERE colors IS NOT NULL LIMIT 5');
    $productColors = $stmt2->fetchAll(PDO::FETCH_COLUMN);
    echo "Sample product colors (JSON): \n";
    foreach ($productColors as $color) {
        echo "- " . $color . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
