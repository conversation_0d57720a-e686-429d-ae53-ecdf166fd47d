<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/config/database.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Wolffoxx\Config\Database;
use Wolffoxx\Models\Outfit;

// Initialize database
Database::init();

echo "🔍 Debugging outfit API response...\n";
echo "===================================\n\n";

try {
    $outfitModel = new Outfit();
    $userId = 3; // Test user
    
    echo "Getting outfits for user $userId...\n";
    $outfits = $outfitModel->getUserOutfits($userId);
    
    echo "API Response Structure:\n";
    echo json_encode($outfits, JSON_PRETTY_PRINT) . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
