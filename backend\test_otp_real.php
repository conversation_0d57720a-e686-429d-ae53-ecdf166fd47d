<?php

/**
 * Real OTP Test Script
 * 
 * This script tests the actual OTP sending functionality with Fast2SMS
 * using the fixed endpoint (fast2sms.com instead of www.fast2sms.com)
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/src/Config/Database.php';

use Wolffoxx\Services\SMSService;
use Wolffoxx\Services\OTPService;

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value, '"');
    }
}

echo "📱 Real OTP Test with Fixed Fast2SMS Endpoint\n";
echo "=============================================\n\n";

// Check configuration
echo "1. Configuration Check...\n";
echo "SMS Provider: " . ($_ENV['SMS_PROVIDER'] ?? 'not set') . "\n";
echo "Fast2SMS API Key: " . (empty($_ENV['FAST2SMS_API_KEY']) ? '❌ Not set' : '✅ Set') . "\n";
echo "Fast2SMS Endpoint: https://fast2sms.com/dev/bulkV2 (FIXED - removed www)\n\n";

// Test with a real phone number (you can change this)
$testPhone = '**********'; // Your phone number from the logs
echo "2. Testing OTP Generation and Sending...\n";
echo "Test Phone: +91$testPhone\n";
echo "⚠️  This will send a REAL SMS to the above number!\n";

// Ask for confirmation
echo "\nDo you want to proceed? (y/n): ";
$handle = fopen("php://stdin", "r");
$confirmation = trim(fgets($handle));
fclose($handle);

if (strtolower($confirmation) !== 'y') {
    echo "Test cancelled.\n";
    exit(0);
}

try {
    // Initialize services
    $otpService = new OTPService();
    
    echo "\n3. Sending OTP...\n";
    $result = $otpService->generateAndSendOTP($testPhone, 'login');
    
    if ($result['success']) {
        echo "✅ OTP sent successfully!\n";
        echo "Message: " . $result['message'] . "\n";
        echo "Phone: " . $result['phone'] . "\n";
        echo "Expires in: " . $result['expires_in'] . " seconds\n";
        echo "Can resend after: " . $result['can_resend_after'] . " seconds\n\n";
        
        // Ask for OTP verification
        echo "4. OTP Verification Test...\n";
        echo "Please enter the OTP you received: ";
        $handle = fopen("php://stdin", "r");
        $enteredOTP = trim(fgets($handle));
        fclose($handle);
        
        if (!empty($enteredOTP)) {
            echo "\nVerifying OTP: $enteredOTP\n";
            $verifyResult = $otpService->verifyOTP($testPhone, $enteredOTP, 'login');
            
            if ($verifyResult['success']) {
                echo "✅ OTP verified successfully!\n";
                echo "Message: " . $verifyResult['message'] . "\n";
            } else {
                echo "❌ OTP verification failed!\n";
                echo "Error: " . $verifyResult['error'] . "\n";
            }
        } else {
            echo "No OTP entered, skipping verification.\n";
        }
        
    } else {
        echo "❌ Failed to send OTP!\n";
        echo "Error: " . $result['error'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Exception occurred: " . $e->getMessage() . "\n";
}

echo "\n5. Check SMS Logs...\n";
$logFile = __DIR__ . '/storage/logs/sms.log';
if (file_exists($logFile)) {
    echo "Latest SMS log entries:\n";
    $logs = file($logFile);
    $recentLogs = array_slice($logs, -3); // Last 3 entries
    foreach ($recentLogs as $log) {
        echo "   " . trim($log) . "\n";
    }
} else {
    echo "SMS log file not found.\n";
}

echo "\n🎉 Test completed!\n";
echo "If the OTP was sent successfully, the DNS issue has been resolved.\n";
echo "Your Fast2SMS integration is now working with the corrected endpoint.\n";
