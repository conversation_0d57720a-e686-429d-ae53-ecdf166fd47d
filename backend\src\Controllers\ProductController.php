<?php

namespace Wolffoxx\Controllers;

use Wolffoxx\Models\Product;
use Wolffoxx\Utils\Response;
use Wolffoxx\Utils\Logger;

/**
 * Product Controller
 * 
 * Handles product listing, search, filtering,
 * and product detail operations.
 */
class ProductController
{
    private Product $productModel;
    private Logger $logger;

    public function __construct()
    {
        $this->productModel = new Product();
        $this->logger = new Logger('product');
    }

    /**
     * Get products with filtering and pagination
     */
    public function index(array $params = []): void
    {
        try {
            // Get query parameters
            $filters = [
                'category' => $_GET['category'] ?? null,
                'subcategory' => $_GET['subcategory'] ?? null,
                'brand' => $_GET['brand'] ?? null,
                'price_min' => isset($_GET['price_min']) ? (float)$_GET['price_min'] : null,
                'price_max' => isset($_GET['price_max']) ? (float)$_GET['price_max'] : null,
                'is_featured' => isset($_GET['featured']) ? (bool)$_GET['featured'] : null,
                'is_new' => isset($_GET['new']) ? (bool)$_GET['new'] : null,
                'is_bestseller' => isset($_GET['bestseller']) ? (bool)$_GET['bestseller'] : null,
                'is_on_sale' => isset($_GET['on_sale']) ? (bool)$_GET['on_sale'] : null,
                'search' => $_GET['search'] ?? null,
                'sort_by' => $_GET['sort_by'] ?? 'created_at',
                'sort_order' => $_GET['sort_order'] ?? 'DESC'
            ];

            $page = max(1, (int)($_GET['page'] ?? 1));
            $perPage = min(100, max(1, (int)($_GET['per_page'] ?? 20)));

            // Get products
            $products = $this->productModel->getProducts($filters, $page, $perPage);

            // Get total count for pagination
            $totalCount = $this->productModel->getTotalCount($filters);

            $this->logger->info('Products retrieved', [
                'filters' => array_filter($filters),
                'page' => $page,
                'per_page' => $perPage,
                'total_count' => $totalCount,
                'result_count' => count($products)
            ]);

            Response::paginated($products, $totalCount, $page, $perPage, [
                'filters' => array_filter($filters)
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Get products failed', [
                'filters' => $_GET,
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to retrieve products');
        }
    }

    /**
     * Get single product details
     */
    public function show(array $params = []): void
    {
        try {
            $productId = (int)($params['id'] ?? 0);

            if (!$productId) {
                Response::error('Product ID is required', 400);
                return;
            }

            $product = $this->productModel->getProductDetailsForFrontend($productId);

            if (!$product) {
                Response::notFound('Product not found');
                return;
            }

            $this->logger->info('Product details retrieved', [
                'product_id' => $productId,
                'product_name' => $product['name']
            ]);

            Response::success($product);

        } catch (\Exception $e) {
            $this->logger->error('Get product details failed', [
                'product_id' => $params['id'] ?? null,
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to retrieve product details');
        }
    }

    /**
     * Get products by category
     */
    public function getByCategory(array $params = []): void
    {
        try {
            $category = $params['category'] ?? '';

            if (empty($category)) {
                Response::error('Category is required', 400);
                return;
            }

            // URL decode the category name
            $category = urldecode($category);

            // Debug logging
            error_log("Category search for: " . $category);

            // Get query parameters
            $filters = [
                'subcategory' => $_GET['subcategory'] ?? null,
                'brand' => $_GET['brand'] ?? null,
                'price_min' => isset($_GET['price_min']) ? (float)$_GET['price_min'] : null,
                'price_max' => isset($_GET['price_max']) ? (float)$_GET['price_max'] : null,
                'is_featured' => isset($_GET['featured']) ? (bool)$_GET['featured'] : null,
                'is_new' => isset($_GET['new']) ? (bool)$_GET['new'] : null,
                'is_bestseller' => isset($_GET['bestseller']) ? (bool)$_GET['bestseller'] : null,
                'is_on_sale' => isset($_GET['on_sale']) ? (bool)$_GET['on_sale'] : null,
                'sort_by' => $_GET['sort_by'] ?? 'created_at',
                'sort_order' => $_GET['sort_order'] ?? 'DESC'
            ];

            $page = max(1, (int)($_GET['page'] ?? 1));
            $perPage = min(100, max(1, (int)($_GET['per_page'] ?? 20)));

            // Get products by category
            $products = $this->productModel->getByCategory($category, $filters, $page, $perPage);

            // Get total count for pagination
            $filters['category'] = $category;
            $totalCount = $this->productModel->getTotalCount($filters);

            $this->logger->info('Products by category retrieved', [
                'category' => $category,
                'filters' => array_filter($filters),
                'total_count' => $totalCount,
                'result_count' => count($products)
            ]);

            // Return empty result if no products found (not an error)
            Response::paginated($products, $totalCount, $page, $perPage, [
                'category' => $category,
                'filters' => array_filter($filters)
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Get products by category failed', [
                'category' => $params['category'] ?? null,
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to retrieve products by category');
        }
    }

    /**
     * Search products
     */
    public function search(array $params = []): void
    {
        try {
            $query = $_GET['q'] ?? '';

            if (empty($query)) {
                Response::error('Search query is required', 400);
                return;
            }

            // Get query parameters
            $filters = [
                'category' => $_GET['category'] ?? null,
                'subcategory' => $_GET['subcategory'] ?? null,
                'brand' => $_GET['brand'] ?? null,
                'price_min' => isset($_GET['price_min']) ? (float)$_GET['price_min'] : null,
                'price_max' => isset($_GET['price_max']) ? (float)$_GET['price_max'] : null,
                'is_featured' => isset($_GET['featured']) ? (bool)$_GET['featured'] : null,
                'is_new' => isset($_GET['new']) ? (bool)$_GET['new'] : null,
                'is_bestseller' => isset($_GET['bestseller']) ? (bool)$_GET['bestseller'] : null,
                'is_on_sale' => isset($_GET['on_sale']) ? (bool)$_GET['on_sale'] : null,
                'sort_by' => $_GET['sort_by'] ?? 'relevance',
                'sort_order' => $_GET['sort_order'] ?? 'DESC'
            ];

            $page = max(1, (int)($_GET['page'] ?? 1));
            $perPage = min(100, max(1, (int)($_GET['per_page'] ?? 20)));

            // Search products
            $products = $this->productModel->searchProducts($query, $filters, $page, $perPage);

            // Get total count for pagination
            $filters['search'] = $query;
            $totalCount = $this->productModel->getTotalCount($filters);

            $this->logger->info('Product search performed', [
                'query' => $query,
                'filters' => array_filter($filters),
                'total_count' => $totalCount,
                'result_count' => count($products)
            ]);

            Response::paginated($products, $totalCount, $page, $perPage, [
                'query' => $query,
                'filters' => array_filter($filters)
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Product search failed', [
                'query' => $_GET['q'] ?? null,
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to search products');
        }
    }

    /**
     * Get product categories
     */
    public function getCategories(array $params = []): void
    {
        try {
            $categories = $this->productModel->getCategories();

            $this->logger->info('Categories retrieved', [
                'count' => count($categories)
            ]);

            Response::success($categories);

        } catch (\Exception $e) {
            $this->logger->error('Get categories failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to retrieve categories');
        }
    }

    /**
     * Get featured products
     */
    public function getFeatured(array $params = []): void
    {
        try {
            $limit = min(50, max(1, (int)($_GET['limit'] ?? 12)));

            $filters = [
                'is_featured' => true,
                'sort_by' => 'created_at',
                'sort_order' => 'DESC'
            ];

            $products = $this->productModel->getProducts($filters, 1, $limit);

            $this->logger->info('Featured products retrieved', [
                'count' => count($products),
                'limit' => $limit
            ]);

            Response::success($products);

        } catch (\Exception $e) {
            $this->logger->error('Get featured products failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to retrieve featured products');
        }
    }

    /**
     * Get new products
     */
    public function getNew(array $params = []): void
    {
        try {
            $limit = min(50, max(1, (int)($_GET['limit'] ?? 12)));

            $filters = [
                'is_new' => true,
                'sort_by' => 'created_at',
                'sort_order' => 'DESC'
            ];

            $products = $this->productModel->getProducts($filters, 1, $limit);

            $this->logger->info('New products retrieved', [
                'count' => count($products),
                'limit' => $limit
            ]);

            Response::success($products);

        } catch (\Exception $e) {
            $this->logger->error('Get new products failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to retrieve new products');
        }
    }

    /**
     * Get bestseller products
     */
    public function getBestsellers(array $params = []): void
    {
        try {
            $limit = min(50, max(1, (int)($_GET['limit'] ?? 12)));

            $filters = [
                'is_bestseller' => true,
                'sort_by' => 'total_reviews',
                'sort_order' => 'DESC'
            ];

            $products = $this->productModel->getProducts($filters, 1, $limit);

            $this->logger->info('Bestseller products retrieved', [
                'count' => count($products),
                'limit' => $limit
            ]);

            Response::success($products);

        } catch (\Exception $e) {
            $this->logger->error('Get bestseller products failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to retrieve bestseller products');
        }
    }

    /**
     * Get sale products
     */
    public function getSale(array $params = []): void
    {
        try {
            $page = max(1, (int)($_GET['page'] ?? 1));
            $perPage = min(100, max(1, (int)($_GET['per_page'] ?? 20)));

            $filters = [
                'is_on_sale' => true,
                'category' => $_GET['category'] ?? null,
                'sort_by' => $_GET['sort_by'] ?? 'created_at',
                'sort_order' => $_GET['sort_order'] ?? 'DESC'
            ];

            $products = $this->productModel->getProducts($filters, $page, $perPage);
            $totalCount = $this->productModel->getTotalCount($filters);

            $this->logger->info('Sale products retrieved', [
                'count' => count($products),
                'total_count' => $totalCount,
                'page' => $page
            ]);

            Response::paginated($products, $totalCount, $page, $perPage, [
                'filters' => array_filter($filters)
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Get sale products failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to retrieve sale products');
        }
    }
}
