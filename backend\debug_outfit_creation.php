<?php

echo "🔍 Testing Outfit Creation API...\n";
echo "==================================\n\n";

// Test the outfit creation API endpoint
$url = 'http://localhost:8000/api/v1/outfits';

// Create test outfit data
$outfitData = [
    'name' => 'Test Outfit Name',
    'description' => 'Test outfit description',
    'occasion' => 'casual',
    'season' => 'all',
    'is_public' => false,
    'items' => [
        [
            'product_id' => 1,
            'selected_color' => 'Black',
            'selected_size' => 'M',
            'selected_color_hex' => '#000000',
            'category_type' => 'top',
            'is_primary' => false
        ]
    ]
];

// Create a context for the HTTP request
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Authorization: Bearer test-token'
        ],
        'content' => json_encode($outfitData)
    ]
]);

try {
    echo "1. Sending outfit creation request...\n";
    echo "   Data being sent:\n";
    echo "   " . json_encode($outfitData, JSON_PRETTY_PRINT) . "\n\n";
    
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "   ❌ Failed to get response\n";
        $error = error_get_last();
        if ($error) {
            echo "   Error: " . $error['message'] . "\n";
        }
    } else {
        echo "   ✅ Response received\n";
        $data = json_decode($response, true);
        
        if ($data) {
            echo "   Status: " . ($data['success'] ? 'Success' : 'Error') . "\n";
            
            if ($data['success'] && isset($data['data']['outfit'])) {
                $outfit = $data['data']['outfit'];
                echo "   Created Outfit:\n";
                echo "     ID: {$outfit['id']}\n";
                echo "     Name: '{$outfit['name']}'\n";
                echo "     Description: '{$outfit['description']}'\n";
                echo "     UUID: {$outfit['uuid']}\n";
                echo "     User ID: {$outfit['user_id']}\n";
                echo "     Total Price: \${$outfit['total_price']}\n";
                echo "     Created At: {$outfit['created_at']}\n";
            } else {
                echo "   Error: " . ($data['message'] ?? 'Unknown error') . "\n";
                if (isset($data['errors'])) {
                    echo "   Validation errors:\n";
                    foreach ($data['errors'] as $field => $errors) {
                        echo "     $field: " . implode(', ', $errors) . "\n";
                    }
                }
            }
        } else {
            echo "   ❌ Invalid JSON response\n";
            echo "   Raw response: " . substr($response, 0, 500) . "...\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n✅ API test completed!\n";
