<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/config/database.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Wolffoxx\Config\Database;

// Initialize database
Database::init();

echo "🔍 Checking recent outfits in database...\n";
echo "==========================================\n\n";

try {
    $stmt = Database::execute('SELECT * FROM outfits ORDER BY created_at DESC LIMIT 5');
    $outfits = $stmt->fetchAll();
    
    if (empty($outfits)) {
        echo "❌ No outfits found in database\n";
    } else {
        echo "✅ Found " . count($outfits) . " outfits:\n\n";
        
        foreach ($outfits as $outfit) {
            echo "ID: {$outfit['id']}\n";
            echo "UUID: {$outfit['uuid']}\n";
            echo "Name: {$outfit['name']}\n";
            echo "Description: {$outfit['description']}\n";
            echo "User ID: {$outfit['user_id']}\n";
            echo "Created: {$outfit['created_at']}\n";
            echo "---\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
