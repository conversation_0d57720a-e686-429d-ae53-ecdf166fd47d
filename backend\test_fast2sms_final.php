<?php

/**
 * Final Fast2SMS Test
 * 
 * Test the corrected Fast2SMS implementation
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/src/Config/Database.php';

use Wolffoxx\Services\SMSService;

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value, '"');
    }
}

echo "🧪 Final Fast2SMS Test with Corrected Endpoint\n";
echo "==============================================\n\n";

$apiKey = $_ENV['FAST2SMS_API_KEY'] ?? '';
$testPhone = '9999999999'; // Test number
$testOTP = '123456';

echo "Configuration:\n";
echo "API Key: " . (empty($apiKey) ? '❌ Not set' : '✅ Set') . "\n";
echo "Endpoint: https://www.fast2sms.com/dev/bulkV2 (corrected)\n";
echo "Test Phone: $testPhone\n";
echo "Test OTP: $testOTP\n\n";

if (empty($apiKey)) {
    echo "❌ Cannot test without API key\n";
    exit(1);
}

try {
    echo "Testing SMS Service...\n";
    $smsService = new SMSService();
    
    // Test OTP sending
    $result = $smsService->sendOTP($testPhone, $testOTP, 'login');
    
    if ($result['success']) {
        echo "✅ OTP sent successfully!\n";
        echo "Provider: " . $result['provider'] . "\n";
        echo "Message ID: " . ($result['message_id'] ?? 'N/A') . "\n";
        echo "Message: " . $result['message'] . "\n";
    } else {
        echo "❌ OTP sending failed!\n";
        echo "Error: " . $result['error'] . "\n";
        
        // Let's also test with a direct API call to see the exact response
        echo "\n🔍 Direct API Test...\n";
        
        $endpoint = 'https://www.fast2sms.com/dev/bulkV2';
        $message = "Your Wolffoxx login OTP is: {$testOTP}. Valid for 5 minutes. Do not share this code.";
        $data = [
            'message' => $message,
            'language' => 'english',
            'route' => 'q',
            'numbers' => $testPhone
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'authorization: ' . $apiKey,
            'accept: */*',
            'cache-control: no-cache',
            'content-type: application/json'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "Direct API Results:\n";
        echo "HTTP Code: $httpCode\n";
        if ($error) {
            echo "cURL Error: $error\n";
        } else {
            echo "Response: $response\n";
            
            $result = json_decode($response, true);
            if ($result) {
                echo "Parsed Response:\n";
                echo "  Return: " . ($result['return'] ?? 'N/A') . "\n";
                echo "  Status Code: " . ($result['status_code'] ?? 'N/A') . "\n";
                echo "  Message: " . ($result['message'] ?? 'N/A') . "\n";
                echo "  Request ID: " . ($result['request_id'] ?? 'N/A') . "\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "\n📋 Common Fast2SMS Error Codes:\n";
echo "401 - Invalid API key\n";
echo "412 - Invalid Authentication\n";
echo "427 - Number blocked in DND list\n";
echo "995 - Spamming detected\n";
echo "200 + return:true - Success\n";
echo "200 + return:false - API error (check message)\n";

echo "\n🎯 Next Steps:\n";
echo "1. If you see 'Number blocked in DND list' - use a different test number\n";
echo "2. If you see 'Invalid Authentication' - check your API key\n";
echo "3. If you see 'Spamming detected' - wait a few minutes between tests\n";
echo "4. If successful, test with your real phone number\n";

echo "\n🔧 Test completed!\n";
