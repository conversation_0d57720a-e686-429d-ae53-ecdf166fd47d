-- Fix Product IDs to match frontend expectations
-- This script updates the products table to use integer IDs instead of UUIDs

-- IMPORTANT: Run this script in phpMyAdmin to fix the product ID mismatch

-- Step 1: Check current products
SELECT id, name FROM products ORDER BY name LIMIT 10;

-- Step 2: Backup current IDs (in case we need to rollback)
ALTER TABLE products ADD COLUMN old_uuid_id VARCHAR(255);
UPDATE products SET old_uuid_id = id;

-- Step 3: Temporarily disable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;

-- Step 4: Update products to use integer IDs that match frontend
-- Map your existing products to the frontend product IDs

-- Find the product that matches "Oversized Tokyo Graphic Tee" and set it to ID 1
UPDATE products SET id = '1' WHERE name LIKE '%Tokyo%' OR name LIKE '%Oversized%' LIMIT 1;

-- Find the product that matches "Vintage Wash Oversized Tee" and set it to ID 2
UPDATE products SET id = '2' WHERE name LIKE '%Vintage%' AND name LIKE '%Oversized%' LIMIT 1;

-- Find the product that matches "Streetwear Oversized Tee" and set it to ID 3
UPDATE products SET id = '3' WHERE name LIKE '%Streetwear%' LIMIT 1;

-- Continue mapping other products to IDs 4, 5, 6, etc.
-- You can check your products table and map them accordingly

-- Step 5: Update related tables (wishlists, cart_items, etc.)
-- Update wishlist entries to use new product IDs
UPDATE wishlists w
JOIN products p ON w.product_id = p.old_uuid_id
SET w.product_id = p.id;

-- Update cart items to use new product IDs
UPDATE cart_items ci
JOIN products p ON ci.product_id = p.old_uuid_id
SET ci.product_id = p.id;

-- Step 6: Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Step 7: Verify the changes
SELECT id, name, old_uuid_id FROM products ORDER BY CAST(id AS UNSIGNED) LIMIT 10;
SELECT * FROM wishlists LIMIT 5;

-- Step 8: Clean up (optional - run this after verifying everything works)
-- ALTER TABLE products DROP COLUMN old_uuid_id;
