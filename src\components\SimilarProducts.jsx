import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { dataService } from '../services/dataService';
import WishlistButton from './WishlistButton';

export default function SimilarProducts({ currentProductId, category }) {
  const [similarProducts, setSimilarProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadSimilarProducts = async () => {
      try {
        setLoading(true);
        const similar = await dataService.getSimilarProducts(currentProductId, category, 8);
        setSimilarProducts(similar);
      } catch (error) {
        console.error('Failed to load similar products:', error);
        setSimilarProducts([]);
      } finally {
        setLoading(false);
      }
    };

    if (currentProductId && category) {
      loadSimilarProducts();
    }
  }, [currentProductId, category]);

  return (
    <div className="mt-16 pt-10 border-t border-[#2a2a2a]">
      <h2 className="text-2xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-8">
        YOU MIGHT ALSO LIKE
      </h2>

      {/* Loading State */}
      {loading ? (
        <div className="flex gap-4 md:gap-6 overflow-x-auto scrollbar-hide pb-4">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="flex-none w-64 md:w-72">
              <div className="bg-[#0a0a0a] rounded-xl overflow-hidden border border-[#404040] animate-pulse">
                <div className="h-72 md:h-80 bg-[#2a2a2a]"></div>
                <div className="p-4 md:p-5">
                  <div className="h-4 bg-[#2a2a2a] rounded mb-2"></div>
                  <div className="h-6 bg-[#2a2a2a] rounded mb-3"></div>
                  <div className="h-5 bg-[#2a2a2a] rounded w-20"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        /* Horizontal Scroller Container */
        <div className="relative">
          <div className="flex gap-4 md:gap-6 overflow-x-auto scrollbar-hide pb-4 snap-x snap-mandatory pr-4">
            {similarProducts.map((product, index) => (
              <div key={product.id} className="flex-none w-64 md:w-72 snap-start">
                <SimilarProductCard product={product} index={index} />
              </div>
            ))}
          </div>

          {/* Right Gradient Overlay only - to show there are more cards */}
          <div className="absolute right-0 top-0 bottom-4 w-6 bg-gradient-to-l from-black to-transparent pointer-events-none z-10" />
        </div>
      )}
    </div>
  );
}

function SimilarProductCard({ product, index }) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedColorIndex, setSelectedColorIndex] = useState(0);

  // Get current images based on selected color (like ProductCard.jsx)
  const getCurrentImages = () => {
    if (product.colors && product.colors[selectedColorIndex] && product.colors[selectedColorIndex].images) {
      return product.colors[selectedColorIndex].images;
    }
    return product.images || [];
  };

  const currentImages = getCurrentImages();

  const handleColorSelect = (colorIndex) => {
    setSelectedColorIndex(colorIndex);
    // Reset to first image when color changes (like ProductCard.jsx)
    setCurrentImageIndex(0);
  };

  const nextImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    setCurrentImageIndex((prev) =>
      prev === currentImages.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    setCurrentImageIndex((prev) =>
      prev === 0 ? currentImages.length - 1 : prev - 1
    );
  };

  const goToImage = (imageIndex, e) => {
    e.preventDefault();
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    setCurrentImageIndex(imageIndex);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true, margin: "-50px" }}
      className="bg-[#0a0a0a] rounded-xl overflow-hidden group border border-[#404040]"
    >
      <Link to={`/product/${product.id}`} className="block relative">
        {/* Image Carousel Container */}
        <div className="h-72 md:h-80 overflow-hidden relative">
          <div className="relative w-full h-full">
            {currentImages.map((image, idx) => (
              <img
                key={idx}
                src={image}
                alt={`${product.name} - Image ${idx + 1}`}
                loading="lazy"
                className={`absolute inset-0 w-full h-full object-cover transform group-hover:scale-105 transition-all duration-700 ease-in-out ${
                  idx === currentImageIndex
                    ? 'opacity-100 z-10'
                    : 'opacity-0 z-0'
                }`}
                onError={(e) => {
                  e.target.src = '/api/placeholder/400/400';
                }}
              />
            ))}
          </div>

          {/* Navigation Arrows - Always visible */}
          {currentImages.length > 1 && (
            <>
              <button
                onClick={prevImage}
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
                className="absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-[#2a2a2a] hover:bg-[#404040] text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 opacity-100"
              >
                <ChevronLeft size={16} />
              </button>
              <button
                onClick={nextImage}
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
                className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-[#2a2a2a] hover:bg-[#404040] text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 opacity-100"
              >
                <ChevronRight size={16} />
              </button>
            </>
          )}

          {/* Image Dots Indicator */}
          {currentImages.length > 1 && (
            <div className="absolute bottom-3 left-1/2 -translate-x-1/2 flex gap-1.5 z-20">
              {currentImages.map((_, idx) => (
                <button
                  key={idx}
                  onClick={(e) => goToImage(idx, e)}
                  onMouseDown={(e) => e.stopPropagation()}
                  onTouchStart={(e) => e.stopPropagation()}
                  className={`w-2 h-2 rounded-full transition-all duration-200 ${
                    idx === currentImageIndex
                      ? 'bg-white scale-110'
                      : 'bg-white/50 hover:bg-white/75'
                  }`}
                />
              ))}
            </div>
          )}

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>

        {/* Badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1 z-10">
          {product.isNew && (
            <div className="bg-[#3b82f6] text-white text-[10px] font-medium px-1.5 py-0.5 rounded-md shadow-lg">
              NEW
            </div>
          )}
          {product.salePrice && (
            <div className="bg-red-500 text-white text-[10px] font-medium px-1.5 py-0.5 rounded-md shadow-lg">
              SALE
            </div>
          )}
        </div>

        {/* Image Counter */}
        {currentImages.length > 1 && (
          <div className="absolute top-3 right-12 bg-black/50 text-white text-xs px-2 py-1 rounded-full backdrop-blur-sm">
            {currentImageIndex + 1}/{currentImages.length}
          </div>
        )}

        {/* Wishlist Button */}
        <div className="absolute top-3 right-3 z-10">
          <WishlistButton
            productId={product.id}
            productName={product.name}
            productPrice={product.salePrice || product.price}
            productImage={product.images[0]}
            className="bg-[#2a2a2a] hover:bg-[#404040] text-white w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200"
          />
        </div>
      </Link>

      {/* Product Info */}
      <div className="p-4 md:p-5">
        <div className="mb-3">
          <span className="text-[#94a3b8] text-xs font-medium uppercase tracking-wide">
            {product.category}
          </span>
          <h3 className="text-[#e2e8f0] font-semibold text-lg hover:text-[#3b82f6] transition-colors mt-1 line-clamp-1 truncate">
            <Link to={`/product/${product.id}`} className="block truncate" title={product.name}>{product.name}</Link>
          </h3>
        </div>

        {/* Price */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            {product.salePrice ? (
              <>
                <span className="text-white font-bold text-lg">${product.salePrice}</span>
                <span className="text-[#94a3b8] line-through text-sm">${product.price}</span>
                <span className="text-xs bg-red-500/20 text-red-400 px-2 py-1 rounded-full font-medium">
                  {Math.round(((product.price - product.salePrice) / product.price) * 100)}% OFF
                </span>
              </>
            ) : (
              <span className="text-white font-bold text-lg">${product.price}</span>
            )}
          </div>
        </div>

        {/* Color Selection */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="flex gap-1.5">
              {product.colors.slice(0, 4).map((color, idx) => (
                <button
                  key={idx}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleColorSelect(idx);
                  }}
                  className={`w-5 h-5 rounded-full border-2 transition-all duration-200 ${
                    selectedColorIndex === idx
                      ? 'border-white scale-110 shadow-lg shadow-white/25'
                      : 'border-[#404040] hover:border-[#6a6a6a]'
                  }`}
                  style={{ backgroundColor: color.value }}
                  title={color.name}
                />
              ))}
              {product.colors.length > 4 && (
                <div className="w-5 h-5 rounded-full bg-[#2a2a2a] flex items-center justify-center text-xs text-[#9a9a9a] font-medium">
                  +{product.colors.length - 4}
                </div>
              )}
            </div>
          </div>

          {/* Stock Status */}
          <div className="text-xs text-gray-400">
            {product.stock > 0 ? (
              <span className="text-green-400">● In Stock</span>
            ) : (
              <span className="text-green-400">● In Stock</span>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}