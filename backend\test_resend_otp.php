<?php

/**
 * Test Resend OTP Functionality
 * 
 * This script tests the resend OTP functionality with real Fast2SMS
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/src/Config/Database.php';

use Wolffoxx\Services\OTPService;

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value, '"');
    }
}

echo "📱 Resend OTP Test with Real Fast2SMS\n";
echo "=====================================\n\n";

$testPhone = '**********'; // Your phone number
echo "Test Phone: +91$testPhone\n";
echo "SMS Provider: " . ($_ENV['SMS_PROVIDER'] ?? 'not set') . "\n\n";

try {
    $otpService = new OTPService();
    
    echo "Step 1: Sending initial OTP...\n";
    $result1 = $otpService->generateAndSendOTP($testPhone, 'login');
    
    if ($result1['success']) {
        echo "✅ Initial OTP sent successfully!\n";
        echo "Message: " . $result1['message'] . "\n";
        echo "Phone: " . $result1['phone'] . "\n";
        echo "Expires in: " . $result1['expires_in'] . " seconds\n";
        echo "Can resend after: " . $result1['can_resend_after'] . " seconds\n\n";
        
        echo "Waiting 18 seconds before testing resend...\n";
        sleep(18); // Wait 18 seconds (more than the 17-second UI timer)
        
        echo "\nStep 2: Testing resend OTP...\n";
        $result2 = $otpService->resendOTP($testPhone, 'login');
        
        if ($result2['success']) {
            echo "✅ OTP resent successfully!\n";
            echo "Message: " . $result2['message'] . "\n";
            echo "Phone: " . $result2['phone'] . "\n";
            echo "Expires in: " . $result2['expires_in'] . " seconds\n";
            echo "Can resend after: " . $result2['can_resend_after'] . " seconds\n\n";
            
            echo "🎉 Resend OTP functionality is working!\n";
            echo "You should have received 2 SMS messages with different OTP codes.\n";
        } else {
            echo "❌ Resend OTP failed!\n";
            echo "Error: " . $result2['error'] . "\n";
        }
        
    } else {
        echo "❌ Initial OTP failed!\n";
        echo "Error: " . $result1['error'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "\n📋 Testing Summary:\n";
echo "- Initial OTP: Tests basic OTP sending\n";
echo "- 18-second wait: Simulates user waiting for resend button\n";
echo "- Resend OTP: Tests resend functionality\n";
echo "- Both should send real SMS via Fast2SMS\n";

echo "\n🔧 Frontend Integration:\n";
echo "- Resend button enables after 17 seconds\n";
echo "- Circular progress shows countdown\n";
echo "- Real OTP sent via Fast2SMS API\n";
echo "- Rate limiting: 60 seconds between requests\n";

echo "\n✅ Test completed!\n";
