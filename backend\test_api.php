<?php

/**
 * API Test Script
 * 
 * Quick test to verify backend APIs are working
 */

// Set the base URL for your backend
$baseUrl = 'http://localhost/backend';

echo "🧪 Testing Wolffoxx Backend APIs\n";
echo "================================\n\n";

// Test 1: Check if backend is running
echo "1. Testing Backend Status...\n";
$response = makeRequest($baseUrl . '/');
if ($response) {
    echo "✅ Backend is running!\n";
    echo "Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n\n";
} else {
    echo "❌ Backend is not responding\n\n";
}

// Test 2: Get all products
echo "2. Testing Products API...\n";
$response = makeRequest($baseUrl . '/api/v1/products');
if ($response && isset($response['data'])) {
    echo "✅ Products API working!\n";
    echo "Found " . count($response['data']) . " products\n";
    if (!empty($response['data'])) {
        echo "Sample product: " . $response['data'][0]['name'] . "\n";
    }
    echo "\n";
} else {
    echo "❌ Products API failed\n";
    echo "Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n\n";
}

// Test 3: Get categories
echo "3. Testing Categories API...\n";
$response = makeRequest($baseUrl . '/api/v1/categories');
if ($response && isset($response['data'])) {
    echo "✅ Categories API working!\n";
    echo "Found " . count($response['data']) . " categories\n";
    foreach ($response['data'] as $category) {
        echo "- " . $category['name'] . "\n";
    }
    echo "\n";
} else {
    echo "❌ Categories API failed\n";
    echo "Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n\n";
}

// Test 4: Get products by category
echo "4. Testing Products by Category...\n";
$response = makeRequest($baseUrl . '/api/v1/products/category/Oversized%20Tees');
if ($response && isset($response['data'])) {
    echo "✅ Category products API working!\n";
    echo "Found " . count($response['data']) . " oversized tees\n\n";
} else {
    echo "❌ Category products API failed\n";
    echo "Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n\n";
}

// Test 5: Get single product
echo "5. Testing Single Product API...\n";
$response = makeRequest($baseUrl . '/api/v1/products/1');
if ($response && isset($response['data'])) {
    echo "✅ Single product API working!\n";
    $product = $response['data'];
    echo "Product: " . $product['name'] . "\n";
    echo "Price: $" . $product['price'] . "\n";
    echo "Colors: " . count($product['colors'] ?? []) . "\n";
    echo "Sizes: " . count($product['sizes'] ?? []) . "\n";
    echo "Images: " . count($product['images'] ?? []) . "\n\n";
} else {
    echo "❌ Single product API failed\n";
    echo "Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n\n";
}

// Test 6: Test OTP Send (without actually sending)
echo "6. Testing OTP Send API...\n";
$otpData = ['phone' => '7982748092'];
$response = makeRequest($baseUrl . '/api/v1/auth/otp/send', 'POST', $otpData);
if ($response && $response['success']) {
    echo "✅ OTP Send API working!\n";
    echo "Message: " . $response['message'] . "\n\n";
} else {
    echo "❌ OTP Send API failed\n";
    echo "Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n\n";
}

echo "🎉 API Testing Complete!\n";
echo "========================\n\n";

echo "📋 NEXT STEPS:\n";
echo "1. All basic APIs are working\n";
echo "2. Your frontend can now connect to backend\n";
echo "3. Ready to continue with Orders & Payment integration\n\n";

echo "🔗 API Endpoints Available:\n";
echo "- GET /api/v1/products - Get all products\n";
echo "- GET /api/v1/products/{id} - Get single product\n";
echo "- GET /api/v1/products/category/{category} - Get products by category\n";
echo "- GET /api/v1/categories - Get all categories\n";
echo "- POST /api/v1/auth/otp/send - Send OTP\n";
echo "- POST /api/v1/auth/otp/verify - Verify OTP\n";
echo "- GET /api/v1/cart - Get user cart (requires auth)\n";
echo "- POST /api/v1/cart/items - Add item to cart (requires auth)\n\n";

/**
 * Make HTTP request
 */
function makeRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Content-Length: ' . strlen(json_encode($data))
            ]);
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        echo "CURL Error: " . curl_error($ch) . "\n";
        curl_close($ch);
        return false;
    }
    
    curl_close($ch);
    
    if ($httpCode !== 200) {
        echo "HTTP Error: $httpCode\n";
        echo "Response: $response\n";
        return false;
    }
    
    return json_decode($response, true);
}
