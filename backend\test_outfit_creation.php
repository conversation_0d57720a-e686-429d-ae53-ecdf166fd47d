<?php

/**
 * Test Outfit Creation API
 * 
 * Direct test of the outfit creation functionality
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Wolffoxx\Config\Database;

// Test outfit creation data
$testOutfitData = [
    'name' => 'Test Outfit from Script',
    'description' => 'Testing outfit creation with 2 items',
    'occasion' => 'casual',
    'season' => 'all',
    'is_public' => false,
    'items' => [
        [
            'product_id' => 1,
            'selected_color' => 'Black',
            'selected_size' => 'M',
            'category_type' => 'top',
            'is_primary' => true
        ],
        [
            'product_id' => 2,
            'selected_color' => 'Blue',
            'selected_size' => 'L',
            'category_type' => 'top',
            'is_primary' => false
        ]
    ]
];

echo "🧪 Testing Outfit Creation API\n";
echo "==============================\n\n";

// Test 1: Direct API call
echo "Test 1: Direct API Call\n";
echo "-----------------------\n";

$url = 'http://localhost:8000/api/v1/outfits';
$data = json_encode($testOutfitData);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer test-token'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_VERBOSE, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
if ($error) {
    echo "cURL Error: $error\n";
}
echo "Response: $response\n\n";

// Test 2: Check database directly
echo "Test 2: Database Check\n";
echo "---------------------\n";

try {
    // Check outfits table
    $sql = "SELECT * FROM outfits ORDER BY created_at DESC LIMIT 5";
    $stmt = Database::execute($sql);
    $outfits = $stmt->fetchAll();
    
    echo "Recent outfits in database:\n";
    foreach ($outfits as $outfit) {
        echo "- ID: {$outfit['id']}, Name: {$outfit['name']}, User: {$outfit['user_id']}, Items: ";
        
        // Check outfit items
        $itemSql = "SELECT COUNT(*) as count FROM outfit_items WHERE outfit_id = ?";
        $itemStmt = Database::execute($itemSql, [$outfit['id']]);
        $itemCount = $itemStmt->fetch()['count'];
        echo "$itemCount\n";
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Check products exist
echo "Test 3: Product Existence Check\n";
echo "-------------------------------\n";

try {
    foreach ($testOutfitData['items'] as $item) {
        $sql = "SELECT id, name, category, price FROM products WHERE id = ?";
        $stmt = Database::execute($sql, [$item['product_id']]);
        $product = $stmt->fetch();
        
        if ($product) {
            echo "✅ Product {$item['product_id']}: {$product['name']} ({$product['category']}) - \${$product['price']}\n";
        } else {
            echo "❌ Product {$item['product_id']}: NOT FOUND\n";
        }
    }
} catch (Exception $e) {
    echo "Product check error: " . $e->getMessage() . "\n";
}

echo "\nTest completed!\n";
