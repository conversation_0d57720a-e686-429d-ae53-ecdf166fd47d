<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/config/database.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Wolffoxx\Config\Database;
use Wolffoxx\Models\Outfit;

// Initialize database
Database::init();

echo "🔍 Testing enhanced outfit fetch...\n";
echo "===================================\n\n";

try {
    $outfitModel = new Outfit();
    
    // Get user outfits (using user ID 3 as test)
    $userId = 3;
    $outfits = $outfitModel->getUserOutfits($userId);
    
    echo "Found " . count($outfits) . " outfits for user $userId\n\n";
    
    foreach ($outfits as $outfit) {
        echo "Outfit: {$outfit['name']} (ID: {$outfit['id']})\n";
        echo "Items: " . count($outfit['items']) . "\n";
        
        foreach ($outfit['items'] as $item) {
            echo "  - Product: {$item['product_name']} (ID: {$item['product_id']})\n";
            echo "    Selected Color: {$item['selected_color']}\n";
            echo "    Selected Color Hex: {$item['selected_color_hex']}\n";
            echo "    Selected Size: {$item['selected_size']}\n";
            echo "    Price at Addition: \${$item['price_at_addition']}\n";
            echo "    Product Image: {$item['product_image']}\n";
            echo "    Available Images: " . (isset($item['images']) ? count($item['images']) : 0) . "\n";
            echo "    Available Colors: " . (isset($item['colors']) ? count($item['colors']) : 0) . "\n";
            echo "    Available Sizes: " . (isset($item['sizes']) ? count($item['sizes']) : 0) . "\n";
            
            if (isset($item['matchingColor'])) {
                echo "    Matching Color: {$item['matchingColor']['name']} ({$item['matchingColor']['hex']})\n";
            }
            
            echo "\n";
        }
        echo "---\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
