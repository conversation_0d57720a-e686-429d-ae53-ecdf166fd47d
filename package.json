{"name": "wolffoxx", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"cloudinary": "^2.7.0", "clsx": "^2.1.1", "framer-motion": "^12.12.1", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-fast-marquee": "^1.6.5", "react-feather": "^2.0.10", "react-range": "^1.10.0", "react-router-dom": "^7.6.0", "react-share": "^5.2.2", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.1.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "vite": "^6.3.5"}}