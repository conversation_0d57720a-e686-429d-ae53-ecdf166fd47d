<?php

/**
 * Test Endpoint Fix
 * 
 * This script verifies that the Fast2SMS endpoint fix is working
 * by testing connectivity without sending actual SMS
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value, '"');
    }
}

echo "🔧 Fast2SMS Endpoint Fix Verification\n";
echo "=====================================\n\n";

// Test the fixed endpoint
$apiKey = $_ENV['FAST2SMS_API_KEY'] ?? '';
$endpoint = 'https://fast2sms.com/dev/bulkV2'; // Fixed endpoint (no www)

echo "1. Testing Fixed Endpoint...\n";
echo "Endpoint: $endpoint\n";
echo "API Key: " . (empty($apiKey) ? '❌ Not set' : '✅ Set') . "\n\n";

if (empty($apiKey)) {
    echo "❌ Cannot test without API key\n";
    exit(1);
}

// Test connectivity with a minimal request
echo "2. Testing Connectivity...\n";
$testData = [
    'message' => 'Test connectivity',
    'language' => 'english',
    'route' => 'q',
    'numbers' => '9999999999' // Dummy number
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $endpoint);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'authorization: ' . $apiKey,
    'accept: */*',
    'cache-control: no-cache',
    'content-type: application/json'
]);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'Wolffoxx-Test/1.0');

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "Results:\n";
if ($error) {
    echo "❌ cURL Error: $error\n";
    echo "   The DNS issue is NOT fixed.\n";
} else {
    echo "✅ Connection successful!\n";
    echo "   HTTP Code: $httpCode\n";
    echo "   Response: " . substr($response, 0, 100) . "...\n";
    echo "   🎉 The DNS issue has been FIXED!\n";
}

echo "\n3. Summary...\n";
if (!$error) {
    echo "✅ Fast2SMS endpoint is now working\n";
    echo "✅ DNS resolution issue resolved\n";
    echo "✅ Your OTP system should now work\n";
    echo "\nNext steps:\n";
    echo "1. Test OTP login in your application\n";
    echo "2. Monitor logs in backend/storage/logs/sms.log\n";
    echo "3. Check that real SMS are being sent\n";
} else {
    echo "❌ Issue still exists\n";
    echo "   Consider switching to MSG91 which was working in your logs\n";
    echo "   Update .env: SMS_PROVIDER=msg91\n";
}

echo "\n🔧 Test completed!\n";
