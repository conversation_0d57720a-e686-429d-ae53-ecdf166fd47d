<?php

/**
 * Simple OTP Test
 */

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value, '"');
    }
}

echo "Simple Fast2SMS Test\n";
echo "===================\n\n";

$apiKey = $_ENV['FAST2SMS_API_KEY'] ?? '';
$endpoint = 'https://www.fast2sms.com/dev/bulkV2';

echo "API Key: " . (empty($apiKey) ? 'Not set' : 'Set') . "\n";
echo "Endpoint: $endpoint\n\n";

if (empty($apiKey)) {
    echo "Error: API key not found\n";
    exit(1);
}

// Test data - using your actual phone number from logs
$data = [
    'message' => 'Your Wolffoxx OTP is: 123456. Valid for 5 minutes.',
    'language' => 'english',
    'route' => 'q',
    'numbers' => '7982748092'  // Your actual number from logs
];

echo "Sending test request...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $endpoint);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_TIMEOUT, 15);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'authorization: ' . $apiKey,
    'content-type: application/json'
]);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "Results:\n";
echo "HTTP Code: $httpCode\n";

if ($error) {
    echo "cURL Error: $error\n";
} else {
    echo "Response: $response\n";
    
    $result = json_decode($response, true);
    if ($result) {
        echo "\nParsed:\n";
        foreach ($result as $key => $value) {
            echo "  $key: $value\n";
        }
    }
}

echo "\nDone.\n";
