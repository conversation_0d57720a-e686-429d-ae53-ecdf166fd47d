import { useState, useEffect, useRef } from 'react';
import { Search, X, TrendingUp, History } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { dataService } from '../services/dataService';

const topSearches = ['Oversized T-shirts', 'Hoodies', 'Winter Collection', 'New Arrivals'];
const trendingCategories = [
  { name: 'Oversized Tees', link: '/category/oversized-tees' },
  { name: 'T-shirts', link: '/category/t-shirts' },
  { name: 'Hoodies', link: '/category/hoodies' },
  { name: 'Shirts', link: '/category/shirts' }
];

export default function SearchBar({ isOpen, onClose }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const inputRef = useRef(null);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
      // Load recent searches from localStorage
      const saved = localStorage.getItem('recentSearches');
      if (saved) {
        setRecentSearches(JSON.parse(saved));
      }
    }
  }, [isOpen]);

  useEffect(() => {
    const searchProducts = async () => {
      if (searchTerm.trim() === '') {
        setSearchResults([]);
        return;
      }

      try {
        setIsSearching(true);
        const response = await dataService.searchProducts(searchTerm, {}, 1, 5);
        setSearchResults(response.products || []);
      } catch (error) {
        console.error('Search failed:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    const timeoutId = setTimeout(searchProducts, 300); // Debounce search
    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const handleSearch = (term) => {
    setSearchTerm(term);
    // Save to recent searches
    const updated = [term, ...recentSearches.filter(s => s !== term)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 bg-black/90 backdrop-blur-md z-50 overflow-auto"
        >
          <motion.div
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="w-full bg-gray-900 border-b border-gray-800"
          >
            <div className="container mx-auto px-4 md:px-8 py-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl text-white font-medium">Search Products</h2>
                <button
                  onClick={onClose}
                  className="p-2 text-gray-400 hover:text-white transition-colors"
                >
                  <X size={24} />
                </button>
              </div>
              
              <div className="relative flex items-center bg-gray-800/50 rounded-lg border border-gray-700/50 overflow-hidden mb-6 group focus-within:border-indigo-500 transition-colors">
                <Search size={20} className="ml-4 text-gray-400" />
                <input
                  ref={inputRef}
                  type="text"
                  placeholder="Search for products..."
                  className="w-full py-4 px-3 bg-transparent text-white border-none outline-none text-lg placeholder:text-gray-500"
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  onKeyDown={handleKeyDown}
                />
                {searchTerm && (
                  <button
                    className="mr-4 p-1.5 text-gray-400 hover:text-white transition-colors rounded-full hover:bg-gray-700"
                    onClick={() => setSearchTerm('')}
                  >
                    <X size={16} />
                  </button>
                )}
              </div>
            </div>
          </motion.div>

          <div className="container mx-auto px-4 md:px-8 py-8">
            <AnimatePresence mode="wait">
              {searchTerm ? (
                <motion.div
                  key="results"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                >
                  {searchResults.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                      {searchResults.map((product) => (
                        <motion.div
                          key={product.id}
                          whileHover={{ scale: 1.02 }}
                          className="bg-gray-800/50 rounded-lg overflow-hidden border border-gray-700/50 hover:border-gray-600/50 transition-colors"
                        >
                          <Link
                            to={`/product/${product.id}`}
                            className="flex items-start gap-4 p-4"
                            onClick={onClose}
                          >
                            <img
                              src={product.images[0]}
                              alt={product.name}
                              className="w-24 h-24 object-cover rounded-md bg-gray-700 flex-shrink-0"
                            />
                            <div>
                              <h3 className="text-white font-medium line-clamp-2">{product.name}</h3>
                              <p className="text-gray-400 text-sm mt-1">{product.category}</p>
                              <div className="flex items-center gap-2 mt-2">
                                {product.is_sale === 1 && product.sale_price ? (
                                  <>
                                    <span className="text-white font-semibold">${product.sale_price}</span>
                                    <span className="text-gray-400 line-through text-sm">${product.price}</span>
                                  </>
                                ) : (
                                  <span className="text-white font-semibold">${product.price}</span>
                                )}
                              </div>
                            </div>
                          </Link>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <p className="text-gray-400 text-lg mb-2">No products found for "{searchTerm}"</p>
                      <p className="text-gray-500">Try searching with different keywords or browse our categories below</p>
                    </div>
                  )}
                </motion.div>
              ) : (
                <motion.div
                  key="suggestions"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  className="grid grid-cols-1 md:grid-cols-2 gap-8"
                >
                  <div>
                    {recentSearches.length > 0 && (
                      <div className="mb-8">
                        <h3 className="text-gray-400 text-sm font-medium uppercase tracking-wider mb-4 flex items-center gap-2">
                          <History size={16} />
                          Recent Searches
                        </h3>
                        <div className="flex flex-wrap gap-2">
                          {recentSearches.map((term, index) => (
                            <button
                              key={index}
                              onClick={() => handleSearch(term)}
                              className="px-4 py-2 bg-gray-800/50 text-gray-300 rounded-full text-sm hover:bg-gray-700/50 hover:text-white transition-colors border border-gray-700/50"
                            >
                              {term}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    <div>
                      <h3 className="text-gray-400 text-sm font-medium uppercase tracking-wider mb-4 flex items-center gap-2">
                        <TrendingUp size={16} />
                        Top Searches
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {topSearches.map((term, index) => (
                          <button
                            key={index}
                            onClick={() => handleSearch(term)}
                            className="px-4 py-2 bg-gray-800/50 text-gray-300 rounded-full text-sm hover:bg-gray-700/50 hover:text-white transition-colors border border-gray-700/50"
                          >
                            {term}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-gray-400 text-sm font-medium uppercase tracking-wider mb-4">Popular Categories</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {trendingCategories.map((category, index) => (
                        <Link
                          key={index}
                          to={category.link}
                          onClick={onClose}
                          className="group p-4 bg-gray-800/50 rounded-lg border border-gray-700/50 hover:border-gray-600/50 transition-all hover:bg-gray-700/50"
                        >
                          <span className="text-gray-300 group-hover:text-white transition-colors">
                            {category.name}
                          </span>
                        </Link>
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}