<?php
/**
 * Debug Authentication Issues
 * 
 * This script helps debug JWT authentication problems
 */

require_once __DIR__ . '/vendor/autoload.php';

use Wolffoxx\Config\Database;
use Wolffoxx\Config\JWTConfig;
use Wolffoxx\Middleware\AuthMiddleware;

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Initialize database
Database::init();

echo "=== JWT Authentication Debug ===\n\n";

// Test 1: Check JWT configuration
echo "1. JWT Configuration:\n";
$config = JWTConfig::getConfig();
print_r($config);
echo "\n";

// Test 2: Generate a test token
echo "2. Generating test token:\n";
$testPayload = [
    'user_id' => 3,
    'email' => '<EMAIL>',
    'role' => 'user'
];

try {
    $tokenPair = JWTConfig::generateTokenPair($testPayload);
    echo "✅ Token generated successfully\n";
    echo "Access Token: " . substr($tokenPair['access_token'], 0, 50) . "...\n";
    echo "Expires in: " . $tokenPair['expires_in'] . " seconds\n\n";
    
    // Test 3: Validate the token
    echo "3. Validating generated token:\n";
    $validatedPayload = JWTConfig::validateToken($tokenPair['access_token']);
    if ($validatedPayload) {
        echo "✅ Token validation successful\n";
        echo "User ID: " . $validatedPayload['user_id'] . "\n";
        echo "Email: " . $validatedPayload['email'] . "\n";
        echo "Expires at: " . date('Y-m-d H:i:s', $validatedPayload['exp']) . "\n\n";
    } else {
        echo "❌ Token validation failed\n\n";
    }
    
    // Test 4: Test header extraction
    echo "4. Testing header extraction:\n";
    $_SERVER['HTTP_AUTHORIZATION'] = 'Bearer ' . $tokenPair['access_token'];
    $extractedToken = JWTConfig::extractTokenFromHeader();
    if ($extractedToken === $tokenPair['access_token']) {
        echo "✅ Header extraction successful\n\n";
    } else {
        echo "❌ Header extraction failed\n";
        echo "Expected: " . substr($tokenPair['access_token'], 0, 20) . "...\n";
        echo "Got: " . substr($extractedToken ?: 'null', 0, 20) . "...\n\n";
    }
    
    // Test 5: Test AuthMiddleware
    echo "5. Testing AuthMiddleware:\n";
    $authMiddleware = new AuthMiddleware();
    
    // Simulate the middleware check
    $result = $authMiddleware->handle();
    if ($result) {
        echo "✅ AuthMiddleware validation successful\n";
        $currentUserId = AuthMiddleware::getCurrentUserId();
        echo "Current User ID: " . $currentUserId . "\n\n";
    } else {
        echo "❌ AuthMiddleware validation failed\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n\n";
}

// Test 6: Check database connection
echo "6. Testing database connection:\n";
try {
    $pdo = Database::getConnection();
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "✅ Database connection successful\n";
    echo "Total users: " . $result['count'] . "\n\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n\n";
}

// Test 7: Check specific user
echo "7. Testing user lookup:\n";
try {
    $stmt = Database::execute("SELECT id, email, first_name, last_name FROM users WHERE id = ?", [3]);
    $user = $stmt->fetch();
    if ($user) {
        echo "✅ User found\n";
        echo "ID: " . $user['id'] . "\n";
        echo "Email: " . $user['email'] . "\n";
        echo "Name: " . $user['first_name'] . " " . $user['last_name'] . "\n\n";
    } else {
        echo "❌ User not found\n\n";
    }
} catch (Exception $e) {
    echo "❌ User lookup failed: " . $e->getMessage() . "\n\n";
}

echo "=== Debug Complete ===\n";
