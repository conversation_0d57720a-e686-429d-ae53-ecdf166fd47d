<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/config/database.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Wolffoxx\Config\Database;

// Initialize database
Database::init();

echo "🔍 Checking outfit names in database...\n";
echo "=======================================\n\n";

try {
    // Get all outfits with their names
    echo "1. All outfits in database:\n";
    $sql = "SELECT id, uuid, user_id, name, description, total_price, created_at FROM outfits ORDER BY created_at DESC";
    $stmt = Database::execute($sql);
    $outfits = $stmt->fetchAll();
    
    foreach ($outfits as $outfit) {
        echo "   ID: {$outfit['id']}\n";
        echo "   Name: '" . ($outfit['name'] ?? 'NULL') . "'\n";
        echo "   Description: '" . ($outfit['description'] ?? 'NULL') . "'\n";
        echo "   UUID: {$outfit['uuid']}\n";
        echo "   User ID: {$outfit['user_id']}\n";
        echo "   Total Price: \${$outfit['total_price']}\n";
        echo "   Created: {$outfit['created_at']}\n";
        echo "   ---\n";
    }
    
    // Check if there are any outfits with NULL names
    echo "\n2. Outfits with NULL names:\n";
    $nullSql = "SELECT COUNT(*) as count FROM outfits WHERE name IS NULL";
    $nullStmt = Database::execute($nullSql);
    $nullResult = $nullStmt->fetch();
    echo "   Count: {$nullResult['count']}\n";
    
    if ($nullResult['count'] > 0) {
        echo "\n3. Fixing NULL names...\n";
        $fixSql = "UPDATE outfits SET name = CONCAT('Outfit ', id) WHERE name IS NULL";
        $fixStmt = Database::execute($fixSql);
        echo "   Updated {$fixStmt->rowCount()} outfits\n";
    }
    
    echo "\n✅ Check completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
