<?php

echo "🔍 Testing Add Item to Outfit API...\n";
echo "====================================\n\n";

// Test the add item to outfit API endpoint
$outfitId = 35; // Use the test outfit we created
$url = "http://localhost:8000/api/v1/outfits/$outfitId/items";

// Create test item data
$itemData = [
    'product_id' => 2, // Different product
    'selected_color' => 'Blue',
    'selected_size' => 'L',
    'selected_color_hex' => '#2563EB',
    'category_type' => 'top',
    'is_primary' => false
];

// Create a context for the HTTP request
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Authorization: Bearer test-token'
        ],
        'content' => json_encode($itemData)
    ]
]);

try {
    echo "1. Adding item to outfit $outfitId...\n";
    echo "   Item data being sent:\n";
    echo "   " . json_encode($itemData, JSON_PRETTY_PRINT) . "\n\n";
    
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "   ❌ Failed to get response\n";
        $error = error_get_last();
        if ($error) {
            echo "   Error: " . $error['message'] . "\n";
        }
    } else {
        echo "   ✅ Response received\n";
        $data = json_decode($response, true);
        
        if ($data) {
            echo "   Status: " . ($data['success'] ? 'Success' : 'Error') . "\n";
            
            if ($data['success'] && isset($data['data']['outfit'])) {
                $outfit = $data['data']['outfit'];
                echo "   Updated Outfit:\n";
                echo "     ID: {$outfit['id']}\n";
                echo "     Name: '{$outfit['name']}'\n";
                echo "     Total Price: \${$outfit['total_price']}\n";
                echo "     Items Count: " . count($outfit['items'] ?? []) . "\n";
                
                if (!empty($outfit['items'])) {
                    echo "   Items in outfit:\n";
                    foreach ($outfit['items'] as $item) {
                        echo "     - Product ID: {$item['product_id']} - {$item['product_name']}\n";
                        echo "       Color: {$item['selected_color']} (Hex: {$item['selected_color_hex']})\n";
                        echo "       Size: {$item['selected_size']}\n";
                        echo "       Price: \${$item['price_at_addition']}\n";
                    }
                }
            } else {
                echo "   Error: " . ($data['message'] ?? 'Unknown error') . "\n";
                if (isset($data['errors'])) {
                    echo "   Validation errors:\n";
                    foreach ($data['errors'] as $field => $errors) {
                        echo "     $field: " . implode(', ', $errors) . "\n";
                    }
                }
            }
        } else {
            echo "   ❌ Invalid JSON response\n";
            echo "   Raw response: " . substr($response, 0, 500) . "...\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n✅ API test completed!\n";
