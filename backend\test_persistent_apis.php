<?php

/**
 * Test script for persistent cart, wishlist, and outfit APIs
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/config/database.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Wolffoxx\Config\Database;

// Initialize database
Database::init();

echo "🧪 Testing Persistent Cart, Wishlist, and Outfit APIs\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// Test database connection
try {
    $connection = Database::getConnection();
    echo "✅ Database connection successful\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test API endpoints
$baseUrl = 'http://localhost:8000/api/v1';

// Test data
$testPhone = '919876543210';
$testOtp = '123456';

echo "\n📱 Testing OTP Authentication...\n";

// Step 1: Send OTP
$sendOtpData = ['phone' => $testPhone];
$response = makeApiCall('POST', $baseUrl . '/auth/otp/send', $sendOtpData);
echo "Send OTP: " . ($response['success'] ? "✅ Success" : "❌ Failed") . "\n";

// Step 2: Verify OTP and get token
$verifyOtpData = ['phone' => $testPhone, 'otp' => $testOtp];
$response = makeApiCall('POST', $baseUrl . '/auth/otp/verify', $verifyOtpData);
$authToken = null;

if ($response['success'] && isset($response['data']['tokens']['access_token'])) {
    $authToken = $response['data']['tokens']['access_token'];
    echo "Verify OTP: ✅ Success - Token obtained\n";
} else {
    echo "Verify OTP: ❌ Failed - " . ($response['error'] ?? 'Unknown error') . "\n";
    exit(1);
}

echo "\n🛒 Testing Cart APIs...\n";

// Test Cart - Get empty cart
$response = makeApiCall('GET', $baseUrl . '/cart', null, $authToken);
echo "Get Cart: " . ($response['success'] ? "✅ Success" : "❌ Failed") . "\n";

// Test Cart - Add item (assuming product ID 1 exists)
$addToCartData = [
    'product_id' => 1,
    'quantity' => 2,
    'selected_color' => 'Black',
    'selected_size' => 'M'
];
$response = makeApiCall('POST', $baseUrl . '/cart/items', $addToCartData, $authToken);
echo "Add to Cart: " . ($response['success'] ? "✅ Success" : "❌ Failed") . "\n";

// Test Cart - Get cart count
$response = makeApiCall('GET', $baseUrl . '/cart/count', null, $authToken);
echo "Get Cart Count: " . ($response['success'] ? "✅ Success" : "❌ Failed") . "\n";

echo "\n❤️ Testing Wishlist APIs...\n";

// Test Wishlist - Get empty wishlist
$response = makeApiCall('GET', $baseUrl . '/wishlist', null, $authToken);
echo "Get Wishlist: " . ($response['success'] ? "✅ Success" : "❌ Failed") . "\n";

// Test Wishlist - Add item
$addToWishlistData = [
    'product_id' => 1,
    'notes' => 'Love this product!',
    'priority' => 'high'
];
$response = makeApiCall('POST', $baseUrl . '/wishlist/items', $addToWishlistData, $authToken);
echo "Add to Wishlist: " . ($response['success'] ? "✅ Success" : "❌ Failed") . "\n";

// Test Wishlist - Get stats
$response = makeApiCall('GET', $baseUrl . '/wishlist/stats', null, $authToken);
echo "Get Wishlist Stats: " . ($response['success'] ? "✅ Success" : "❌ Failed") . "\n";

echo "\n👔 Testing Outfit APIs...\n";

// Test Outfits - Get empty outfits
$response = makeApiCall('GET', $baseUrl . '/outfits', null, $authToken);
echo "Get Outfits: " . ($response['success'] ? "✅ Success" : "❌ Failed") . "\n";

// Test Outfits - Create outfit
$createOutfitData = [
    'name' => 'Casual Weekend Look',
    'description' => 'Perfect for weekend outings',
    'occasion' => 'casual',
    'season' => 'all',
    'is_public' => false,
    'items' => [
        [
            'product_id' => 1,
            'selected_color' => 'Black',
            'selected_size' => 'M',
            'category_type' => 'top',
            'is_primary' => true
        ]
    ]
];
$response = makeApiCall('POST', $baseUrl . '/outfits', $createOutfitData, $authToken);
$outfitId = null;

if ($response['success'] && isset($response['data']['outfit']['id'])) {
    $outfitId = $response['data']['outfit']['id'];
    echo "Create Outfit: ✅ Success - Outfit ID: $outfitId\n";
} else {
    echo "Create Outfit: ❌ Failed - " . ($response['error'] ?? 'Unknown error') . "\n";
}

// Test Outfits - Get single outfit
if ($outfitId) {
    $response = makeApiCall('GET', $baseUrl . '/outfits/' . $outfitId, null, $authToken);
    echo "Get Single Outfit: " . ($response['success'] ? "✅ Success" : "❌ Failed") . "\n";
}

echo "\n🔄 Testing Persistence...\n";

// Simulate logout and login again to test persistence
echo "Simulating logout and login...\n";

// Get new token (simulating fresh login)
$response = makeApiCall('POST', $baseUrl . '/auth/otp/verify', $verifyOtpData);
if ($response['success'] && isset($response['data']['tokens']['access_token'])) {
    $newAuthToken = $response['data']['tokens']['access_token'];
    echo "New login: ✅ Success\n";
    
    // Test if cart persists
    $response = makeApiCall('GET', $baseUrl . '/cart', null, $newAuthToken);
    $cartItems = $response['data']['total_items'] ?? 0;
    echo "Cart Persistence: " . ($cartItems > 0 ? "✅ Success ($cartItems items)" : "❌ Failed (empty cart)") . "\n";
    
    // Test if wishlist persists
    $response = makeApiCall('GET', $baseUrl . '/wishlist', null, $newAuthToken);
    $wishlistItems = count($response['data'] ?? []);
    echo "Wishlist Persistence: " . ($wishlistItems > 0 ? "✅ Success ($wishlistItems items)" : "❌ Failed (empty wishlist)") . "\n";
    
    // Test if outfits persist
    $response = makeApiCall('GET', $baseUrl . '/outfits', null, $newAuthToken);
    $outfitCount = count($response['data']['outfits'] ?? []);
    echo "Outfit Persistence: " . ($outfitCount > 0 ? "✅ Success ($outfitCount outfits)" : "❌ Failed (no outfits)") . "\n";
    
} else {
    echo "New login: ❌ Failed\n";
}

echo "\n🎉 Testing completed!\n";

/**
 * Make API call helper function
 */
function makeApiCall($method, $url, $data = null, $token = null) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => array_filter([
            'Content-Type: application/json',
            $token ? "Authorization: Bearer $token" : null
        ]),
        CURLOPT_POSTFIELDS => $data ? json_encode($data) : null,
        CURLOPT_TIMEOUT => 30
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $decoded = json_decode($response, true);
    
    if ($httpCode >= 400) {
        return ['success' => false, 'error' => $decoded['error'] ?? 'HTTP Error ' . $httpCode];
    }
    
    return $decoded ?: ['success' => false, 'error' => 'Invalid response'];
}
