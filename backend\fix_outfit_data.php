<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/config/database.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Wolffoxx\Config\Database;

// Initialize database
Database::init();

echo "🔧 Fixing outfit data...\n";
echo "========================\n\n";

try {
    // Get color hex for Black
    function getColorHex($colorName) {
        $colorMap = [
            'Black' => '#000000',
            'White' => '#FFFFFF',
            'Red' => '#DC2626',
            'Blue' => '#2563EB',
            'Green' => '#16A34A',
            'Yellow' => '#EAB308',
            'Orange' => '#EA580C',
            'Purple' => '#9333EA',
            'Pink' => '#EC4899',
            'Brown' => '#A16207',
            'Gray' => '#6B7280',
            'Grey' => '#6B7280'
        ];
        return $colorMap[$colorName] ?? '#6B7280';
    }
    
    // Fix outfit item hex color
    echo "1. Fixing outfit item hex color...\n";
    $sql = "UPDATE outfit_items SET selected_color_hex = ? WHERE selected_color = 'Black' AND selected_color_hex IS NULL";
    $stmt = Database::execute($sql, [getColorHex('Black')]);
    echo "   Updated outfit items with Black color hex\n\n";
    
    // Fix outfit totals
    echo "2. Fixing outfit totals...\n";
    $sql = "SELECT id FROM outfits WHERE total_price = 0 OR total_price IS NULL";
    $stmt = Database::execute($sql);
    $outfits = $stmt->fetchAll();
    
    foreach ($outfits as $outfit) {
        $outfitId = $outfit['id'];
        
        // Calculate totals
        $totalSql = "SELECT
                        SUM(price_at_addition) as total_price,
                        SUM(COALESCE(sale_price_at_addition, price_at_addition)) as total_sale_price,
                        COUNT(*) as item_count
                    FROM outfit_items
                    WHERE outfit_id = ?";
        
        $totalStmt = Database::execute($totalSql, [$outfitId]);
        $totals = $totalStmt->fetch();
        
        $totalPrice = $totals['total_price'] ?? 0.00;
        $totalSalePrice = $totals['total_sale_price'] ?? 0.00;
        $itemCount = $totals['item_count'] ?? 0;
        
        // Update outfit
        $updateSql = "UPDATE outfits SET
                        total_price = ?,
                        total_sale_price = ?,
                        is_complete = ?,
                        updated_at = NOW()
                      WHERE id = ?";
        
        Database::execute($updateSql, [
            $totalPrice,
            $totalSalePrice,
            $itemCount > 0,
            $outfitId
        ]);
        
        echo "   Updated outfit {$outfitId}: Total Price = \${$totalPrice}, Items = {$itemCount}\n";
    }
    
    echo "\n3. Verifying fixes...\n";
    $sql = "SELECT o.id, o.name, o.total_price, o.total_sale_price,
                   oi.selected_color, oi.selected_color_hex, oi.price_at_addition
            FROM outfits o
            JOIN outfit_items oi ON o.id = oi.outfit_id
            ORDER BY o.created_at DESC
            LIMIT 5";
    
    $stmt = Database::execute($sql);
    $results = $stmt->fetchAll();
    
    foreach ($results as $row) {
        echo "   Outfit: {$row['name']} (ID: {$row['id']})\n";
        echo "   Total Price: \${$row['total_price']}\n";
        echo "   Color: {$row['selected_color']} (Hex: {$row['selected_color_hex']})\n";
        echo "   Item Price: \${$row['price_at_addition']}\n\n";
    }
    
    echo "✅ Outfit data fixed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
